@page "/access-denied"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MolySite.Web.Services
@inject NavigationManager NavigationManager
@inject ILogger<AccessDenied> Logger
@inject AuthenticationStateProvider AuthStateProvider
@inject IJSInteropService JSInterop
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<PageTitle>访问被拒绝 - MolySite</PageTitle>

<div class="container mt-5">
    <div class="card shadow">
        <div class="card-header bg-danger text-white">
            <h2 class="mb-0">访问被拒绝</h2>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h4>您没有权限访问此页面</h4>
                <p>请确保您已登录并拥有正确的权限。</p>
            </div>

            <AuthorizeView>
                <Authorized>
                    <div class="alert alert-info">
                        <h5>当前用户信息:</h5>
                        <p><strong>用户名:</strong> @context.User.Identity?.Name</p>
                        <p><strong>角色:</strong> @string.Join(", ", context.User.Claims
                            .Where(c => c.Type == ClaimTypes.Role)
                            .Select(c => c.Value))</p>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="alert alert-warning">
                        <p>您尚未登录，请先登录后再尝试访问。</p>
                    </div>
                </NotAuthorized>
            </AuthorizeView>

            <div class="mt-4">
                <button class="btn btn-primary me-2" @onclick="RefreshAuthState">刷新认证状态</button>
                <button class="btn btn-warning me-2" @onclick="RefreshPage">刷新页面</button>
                <button class="btn btn-info me-2" @onclick="CheckCookies">检查Cookie</button>
                <button class="btn btn-secondary me-2" @onclick="CheckLocalStorage">检查本地存储</button>
                <button class="btn btn-success me-2" @onclick="RestoreAuthFromLocalStorage">从本地存储恢复认证</button>
                <a href="/login" class="btn btn-primary me-2">登录</a>
                <a href="/" class="btn btn-secondary">返回首页</a>
            </div>

            @if (_debugInfo.Count > 0)
            {
                <div class="mt-4">
                    <h5>调试信息:</h5>
                    <div class="alert alert-info">
                        <ul>
                            @foreach (var info in _debugInfo)
                            {
                                <li>@info</li>
                            }
                        </ul>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<string> _debugInfo = new List<string>();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        try
        {
            // 获取认证状态
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            
            // 添加调试信息
            _debugInfo.Add($"用户已认证: {user.Identity?.IsAuthenticated}");
            _debugInfo.Add($"用户名: {user.Identity?.Name}");
            
            // 获取所有声明
            var claims = user.Claims.ToList();
            _debugInfo.Add($"声明数量: {claims.Count}");
            
            foreach (var claim in claims)
            {
                _debugInfo.Add($"声明: {claim.Type} = {claim.Value}");
            }
            
            // 特别检查角色声明
            var roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();
            
            _debugInfo.Add($"角色数量: {roles.Count}");
            foreach (var role in roles)
            {
                _debugInfo.Add($"角色: {role}");
            }
            
            Logger.LogInformation("访问被拒绝页面 - 用户认证状态: IsAuthenticated={IsAuthenticated}, UserName={UserName}, Roles={Roles}",
                user.Identity?.IsAuthenticated, user.Identity?.Name, string.Join(", ", roles));
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"获取认证状态时发生错误: {ex.Message}");
            Logger.LogError(ex, "获取认证状态时发生错误");
        }
    }
    
    private async Task RefreshAuthState()
    {
        _debugInfo.Clear();
        _debugInfo.Add("手动刷新认证状态...");
        
        // 强制刷新认证状态
        if (AuthStateProvider is CustomAuthStateProvider customProvider)
        {
            customProvider.ForceRefresh();
            _debugInfo.Add("已强制刷新认证状态缓存");
        }
        
        // 获取认证状态
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        
        // 添加调试信息
        _debugInfo.Add($"用户已认证: {user.Identity?.IsAuthenticated}");
        _debugInfo.Add($"用户名: {user.Identity?.Name}");
        
        // 特别检查角色声明
        var roles = user.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();
        
        _debugInfo.Add($"角色数量: {roles.Count}");
        foreach (var role in roles)
        {
            _debugInfo.Add($"角色: {role}");
        }
        
        StateHasChanged();
    }
    
    private async Task RefreshPage()
    {
        await JSInterop.RefreshPage();
    }
    
    private async Task CheckCookies()
    {
        _debugInfo.Add("检查Cookie...");
        
        try
        {
            var allCookies = await JSInterop.GetAllCookies();
            _debugInfo.Add($"所有Cookie: {allCookies}");
            
            var authCookie = await JSInterop.GetCookie(".AspNetCore.Cookies");
            _debugInfo.Add($"认证Cookie存在: {!string.IsNullOrEmpty(authCookie)}");
            
            var userCookie = await JSInterop.GetCookie("MolySite.AuthUser");
            _debugInfo.Add($"用户Cookie: {userCookie}");
            
            var roleCookie = await JSInterop.GetCookie("MolySite.AuthRole");
            _debugInfo.Add($"角色Cookie: {roleCookie}");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"检查Cookie时发生错误: {ex.Message}");
        }
    }
    
    private async Task CheckLocalStorage()
    {
        _debugInfo.Add("检查本地存储...");
        
        try
        {
            var userJson = await JSInterop.GetLocalStorage("user");
            _debugInfo.Add($"本地存储中的用户信息: {(string.IsNullOrEmpty(userJson) ? "无" : "有")}");
            
            if (!string.IsNullOrEmpty(userJson))
            {
                try
                {
                    var loginResponse = System.Text.Json.JsonSerializer.Deserialize<LoginResponseDto>(userJson, 
                        new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    
                    if (loginResponse != null)
                    {
                        _debugInfo.Add($"用户名: {loginResponse.UserName}");
                        _debugInfo.Add($"角色: {string.Join(", ", loginResponse.Roles)}");
                        _debugInfo.Add($"是否有SuperAdmin角色: {loginResponse.Roles.Contains("SuperAdmin")}");
                    }
                }
                catch (Exception ex)
                {
                    _debugInfo.Add($"解析用户信息时发生错误: {ex.Message}");
                }
            }
            
            var authToken = await JSInterop.GetLocalStorage("authToken");
            _debugInfo.Add($"本地存储中的认证令牌: {(string.IsNullOrEmpty(authToken) ? "无" : "有")}");
            
            var timestamp = await JSInterop.GetLocalStorage("auth_timestamp");
            _debugInfo.Add($"认证时间戳: {timestamp}");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"检查本地存储时发生错误: {ex.Message}");
        }
    }
    
    private async Task RestoreAuthFromLocalStorage()
    {
        _debugInfo.Add("尝试从本地存储恢复认证状态...");
        
        try
        {
            var userJson = await JSInterop.GetLocalStorage("user");
            if (!string.IsNullOrEmpty(userJson))
            {
                _debugInfo.Add("找到本地存储的用户信息，尝试恢复认证状态");
                
                var loginResponse = System.Text.Json.JsonSerializer.Deserialize<LoginResponseDto>(userJson, 
                    new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                
                if (loginResponse != null)
                {
                    _debugInfo.Add($"用户名: {loginResponse.UserName}, 角色: {string.Join(", ", loginResponse.Roles)}");
                    
                    // 如果是CustomAuthStateProvider，直接标记用户为已认证
                    if (AuthStateProvider is CustomAuthStateProvider customProvider)
                    {
                        customProvider.MarkUserAsAuthenticated(loginResponse);
                        _debugInfo.Add("已标记用户为已认证");
                        
                        // 刷新页面
                        await Task.Delay(500); // 给一点时间让认证状态更新
                        await JSInterop.RefreshPage();
                    }
                }
            }
            else
            {
                _debugInfo.Add("本地存储中没有找到用户信息");
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"从本地存储恢复认证状态时发生错误: {ex.Message}");
            Logger.LogError(ex, "从本地存储恢复认证状态时发生错误");
        }
    }
} 
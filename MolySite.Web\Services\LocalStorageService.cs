using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 本地存储服务实现，使用 ProtectedBrowserStorage
    /// </summary>
    public class LocalStorageService : ILocalStorageService
    {
        private readonly ProtectedSessionStorage _sessionStorage;
        private readonly ILogger<LocalStorageService> _logger;

        public LocalStorageService(
            ProtectedSessionStorage sessionStorage,
            ILogger<LocalStorageService> logger)
        {
            _sessionStorage = sessionStorage;
            _logger = logger;
        }

        /// <summary>
        /// 获取存储的值
        /// </summary>
        public async Task<T?> GetItemAsync<T>(string key)
        {
            try
            {
                var result = await _sessionStorage.GetAsync<T>(key);
                return result.Success ? result.Value : default;
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("JavaScript interop"))
            {
                _logger.LogInformation("预渲染期间无法访问本地存储: {Key}", key);
                return default;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "从本地存储获取数据时发生错误: {Key}", key);
                return default;
            }
        }

        /// <summary>
        /// 设置存储的值
        /// </summary>
        public async Task SetItemAsync<T>(string key, T value)
        {
            try
            {
                await _sessionStorage.SetAsync(key, value);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("JavaScript interop"))
            {
                _logger.LogInformation("预渲染期间无法设置本地存储: {Key}", key);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "向本地存储设置数据时发生错误: {Key}", key);
            }
        }

        /// <summary>
        /// 移除存储的值
        /// </summary>
        public async Task RemoveItemAsync(string key)
        {
            try
            {
                await _sessionStorage.DeleteAsync(key);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("JavaScript interop"))
            {
                _logger.LogInformation("预渲染期间无法删除本地存储: {Key}", key);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "从本地存储删除数据时发生错误: {Key}", key);
            }
        }

        /// <summary>
        /// 清除所有存储的值
        /// </summary>
        public async Task ClearAsync()
        {
            try
            {
                // ProtectedSessionStorage 没有直接的清除方法
                // 我们可以通过删除已知的键来实现类似的功能
                await _sessionStorage.DeleteAsync("authToken");
                await _sessionStorage.DeleteAsync("refreshToken");
                await _sessionStorage.DeleteAsync("user");
                await _sessionStorage.DeleteAsync("tenant");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("JavaScript interop"))
            {
                _logger.LogInformation("预渲染期间无法清除本地存储");
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "清除本地存储时发生错误");
            }
        }
    }
} 
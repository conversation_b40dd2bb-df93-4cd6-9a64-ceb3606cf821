﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using MolySite.Web.Components.Layout
@using MolySite.Web.Services
@using MolySite.Web.Components.Pages
@inject ILogger<Routes> Logger
@inject NavigationManager NavigationManager
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<Router AppAssembly="@typeof(Program).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin ReturnUrl="@NavigationManager.ToBaseRelativePath(NavigationManager.Uri)" />
                }
                else
                {
                    <AccessDenied />
                }
            </NotAuthorized>
            <Authorizing>
                <div class="container mt-5 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">正在验证授权...</p>
                </div>
            </Authorizing>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="container mt-5">
                <div class="alert alert-danger">
                    <h3>404 - 页面未找到</h3>
                    <p>抱歉，您请求的页面不存在。</p>
                    <a href="/" class="btn btn-primary">返回首页</a>
                </div>
            </div>
        </LayoutView>
    </NotFound>
</Router>

@code {
    protected override void OnInitialized()
    {
        Logger.LogInformation("Routes组件初始化");
    }
}

@page "/dashboard/superadmin/subscription-plans"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Index> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>订阅计划管理 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">订阅计划管理</h1>
        <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/superadmin/subscription-plans/create"))" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
            创建订阅计划
        </button>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_plans == null || !_plans.Any())
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">暂无订阅计划数据</p>
        </div>
    }
    else
    {
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach (var plan in _plans)
            {
                <div class="bg-white shadow rounded-lg overflow-hidden @(plan.IsDefault ? "border-2 border-blue-500" : "")">
                    <div class="px-6 py-4 @(plan.IsDefault ? "bg-blue-50" : "")">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-bold text-gray-800">@plan.Name</h2>
                            @if (plan.IsDefault)
                            {
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    默认
                                </span>
                            }
                        </div>
                        <div class="mt-2">
                            <span class="text-3xl font-bold text-gray-900">¥@plan.MonthlyPrice</span>
                            <span class="text-gray-500">/月</span>
                        </div>
                        <div class="mt-1 text-gray-500">
                            <span>年付：¥@plan.YearlyPrice</span>
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <p class="text-gray-600 mb-4">@(string.IsNullOrEmpty(plan.Description) ? "-" : plan.Description)</p>
                        <ul class="space-y-2">
                            <li class="flex items-center">
                                <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>最大用户数：@plan.MaxUsers</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>最大存储空间：@(plan.MaxStorageMB / 1024)GB</span>
                            </li>
                            <li class="flex items-center">
                                @if (plan.SupportsCustomDomain)
                                {
                                    <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                }
                                else
                                {
                                    <svg class="h-5 w-5 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                }
                                <span>自定义域名</span>
                            </li>
                            <li class="flex items-center">
                                @if (plan.SupportsAdvancedThemes)
                                {
                                    <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                }
                                else
                                {
                                    <svg class="h-5 w-5 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                }
                                <span>高级主题</span>
                            </li>
                            <li class="flex items-center">
                                @if (plan.SupportsApiAccess)
                                {
                                    <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                }
                                else
                                {
                                    <svg class="h-5 w-5 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                }
                                <span>API访问</span>
                            </li>
                            <li class="flex items-center">
                                @if (plan.SupportsPrioritySupport)
                                {
                                    <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                }
                                else
                                {
                                    <svg class="h-5 w-5 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                }
                                <span>优先技术支持</span>
                            </li>
                        </ul>
                    </div>
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <span class="mr-2">状态：</span>
                                @if (plan.IsActive)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        活跃
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        禁用
                                    </span>
                                }
                            </div>
                            <div class="flex space-x-2">
                                <button @onclick="@(() => NavigationManager.NavigateTo($"/dashboard/superadmin/subscription-plans/edit/{plan.Id}"))" 
                                        class="text-indigo-600 hover:text-indigo-900">
                                    编辑
                                </button>
                                <button @onclick="@(() => ShowDeleteConfirmation(plan))" 
                                        class="text-red-600 hover:text-red-900">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }

    @if (_showDeleteModal)
    {
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
            <div class="bg-white p-5 rounded-lg shadow-lg max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
                <p class="text-gray-600 mb-6">
                    您确定要删除订阅计划 "@_planToDelete?.Name" 吗？此操作无法撤销。
                </p>
                <div class="flex justify-end space-x-3">
                    <button @onclick="CancelDelete" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                        取消
                    </button>
                    <button @onclick="ConfirmDelete" 
                            class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded">
                        删除
                    </button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<SubscriptionPlanDto> _plans = new();
    private bool _loading = true;
    private bool _showDeleteModal = false;
    private SubscriptionPlanDto? _planToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadPlans();
    }

    private async Task LoadPlans()
    {
        try
        {
            _loading = true;
            _plans = await TenantService.GetAllSubscriptionPlansAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载订阅计划列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private void ShowDeleteConfirmation(SubscriptionPlanDto plan)
    {
        _planToDelete = plan;
        _showDeleteModal = true;
    }

    private void CancelDelete()
    {
        _showDeleteModal = false;
        _planToDelete = null;
    }

    private async Task ConfirmDelete()
    {
        if (_planToDelete != null)
        {
            try
            {
                await TenantService.DeleteSubscriptionPlanAsync(_planToDelete.Id);
                _showDeleteModal = false;
                _planToDelete = null;
                await LoadPlans();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除订阅计划时发生错误");
            }
        }
    }
} 
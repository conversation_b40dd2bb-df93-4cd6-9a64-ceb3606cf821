@page "/register"
@using MolySite.Web.Services
@using MolySite.Web.Dtos
@using System.Diagnostics
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject ILogger<Register> Logger
@rendermode InteractiveServer

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">用户注册</h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(_errorMessage))
                    {
                        <div class="alert alert-danger">@_errorMessage</div>
                    }

                    @if (_isLoading)
                    {
                        <div class="text-center mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>正在处理注册请求...</p>
                        </div>
                    }
                    else
                    {
                        <form @onsubmit="HandleRegisterSubmit">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" 
                                       class="form-control @(_usernameError ? "is-invalid" : "")" 
                                       id="username" 
                                       @bind="_username" 
                                       @bind:event="oninput" />
                                @if (_usernameError)
                                {
                                    <div class="invalid-feedback">@_usernameErrorMessage</div>
                                }
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">电子邮件</label>
                                <input type="email" 
                                       class="form-control @(_emailError ? "is-invalid" : "")" 
                                       id="email" 
                                       @bind="_email" 
                                       @bind:event="oninput" />
                                @if (_emailError)
                                {
                                    <div class="invalid-feedback">@_emailErrorMessage</div>
                                }
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" 
                                       class="form-control @(_passwordError ? "is-invalid" : "")" 
                                       id="password" 
                                       @bind="_password" 
                                       @bind:event="oninput" />
                                @if (_passwordError)
                                {
                                    <div class="invalid-feedback">@_passwordErrorMessage</div>
                                }
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认密码</label>
                                <input type="password" 
                                       class="form-control @(_confirmPasswordError ? "is-invalid" : "")" 
                                       id="confirmPassword" 
                                       @bind="_confirmPassword" 
                                       @bind:event="oninput" />
                                @if (_confirmPasswordError)
                                {
                                    <div class="invalid-feedback">@_confirmPasswordErrorMessage</div>
                                }
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">注册</button>
                                <a href="/login" class="btn btn-link">已有账号？登录</a>
                            </div>
                        </form>
                    }

                    @if (_debugInfo.Count > 0)
                    {
                        <div class="mt-4">
                            <h5>调试信息:</h5>
                            <div class="alert alert-info">
                                <ul>
                                    @foreach (var info in _debugInfo)
                                    {
                                        <li>@info</li>
                                    }
                                </ul>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string _username = string.Empty;
    private string _email = string.Empty;
    private string _password = string.Empty;
    private string _confirmPassword = string.Empty;
    
    private string _errorMessage = string.Empty;
    private bool _isLoading = false;
    
    private bool _usernameError = false;
    private bool _emailError = false;
    private bool _passwordError = false;
    private bool _confirmPasswordError = false;
    
    private string _usernameErrorMessage = "用户名是必填项";
    private string _emailErrorMessage = "电子邮件是必填项";
    private string _passwordErrorMessage = "密码是必填项";
    private string _confirmPasswordErrorMessage = "确认密码是必填项";
    
    private List<string> _debugInfo = new List<string>();

    private async Task HandleRegisterSubmit()
    {
        try
        {
            // 清除之前的错误和调试信息
            _errorMessage = string.Empty;
            _debugInfo.Clear();
            
            _usernameError = false;
            _emailError = false;
            _passwordError = false;
            _confirmPasswordError = false;

            AddDebugInfo($"表单提交开始: {DateTime.Now}");

            // 表单验证
            bool isValid = true;
            
            // 验证用户名
            if (string.IsNullOrWhiteSpace(_username))
            {
                _usernameError = true;
                _usernameErrorMessage = "用户名是必填项";
                isValid = false;
                AddDebugInfo("用户名验证失败");
            }
            else if (_username.Length < 3)
            {
                _usernameError = true;
                _usernameErrorMessage = "用户名长度必须至少为3个字符";
                isValid = false;
                AddDebugInfo("用户名长度验证失败");
            }

            // 验证电子邮件
            if (string.IsNullOrWhiteSpace(_email))
            {
                _emailError = true;
                _emailErrorMessage = "电子邮件是必填项";
                isValid = false;
                AddDebugInfo("电子邮件验证失败");
            }
            else if (!IsValidEmail(_email))
            {
                _emailError = true;
                _emailErrorMessage = "请输入有效的电子邮件地址";
                isValid = false;
                AddDebugInfo("电子邮件格式验证失败");
            }

            // 验证密码
            if (string.IsNullOrWhiteSpace(_password))
            {
                _passwordError = true;
                _passwordErrorMessage = "密码是必填项";
                isValid = false;
                AddDebugInfo("密码验证失败");
            }
            else if (_password.Length < 6)
            {
                _passwordError = true;
                _passwordErrorMessage = "密码长度必须至少为6个字符";
                isValid = false;
                AddDebugInfo("密码长度验证失败");
            }

            // 验证确认密码
            if (string.IsNullOrWhiteSpace(_confirmPassword))
            {
                _confirmPasswordError = true;
                _confirmPasswordErrorMessage = "确认密码是必填项";
                isValid = false;
                AddDebugInfo("确认密码验证失败");
            }
            else if (_confirmPassword != _password)
            {
                _confirmPasswordError = true;
                _confirmPasswordErrorMessage = "两次输入的密码不一致";
                isValid = false;
                AddDebugInfo("密码一致性验证失败");
            }

            if (!isValid)
            {
                AddDebugInfo("表单验证失败，不发送请求");
                StateHasChanged();
                return;
            }

            // 显示加载状态
            _isLoading = true;
            StateHasChanged();
            AddDebugInfo("显示加载状态");

            // 准备注册请求
            var registerDto = new RegisterDto
            {
                UserName = _username,
                Email = _email,
                Password = _password,
                ConfirmPassword = _confirmPassword,
                TenantId = await GetDefaultTenantIdAsync() // 这将调用新添加的方法
            };

            AddDebugInfo($"准备发送注册请求: {_username}, {_email}");

            // 发送注册请求
            var result = await AuthService.RegisterAsync(registerDto);
            
            AddDebugInfo($"收到注册响应: Success={result.Success}");

            if (result.Success)
            {
                AddDebugInfo($"注册成功: {result.UserId}");
                
                AddDebugInfo("准备重定向到登录页面");
                NavigationManager.NavigateTo("/login");
                return;
            }
            else
            {
                _errorMessage = result.ErrorMessage ?? "注册失败，请检查输入信息";
                AddDebugInfo($"注册失败: {_errorMessage}");
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"注册过程中发生错误: {ex.Message}";
            AddDebugInfo($"异常: {ex.Message}");
            Logger.LogError(ex, "注册过程中发生异常");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void AddDebugInfo(string info)
    {
        _debugInfo.Add($"{_debugInfo.Count + 1}. {info}");
        Logger.LogInformation(info);
    }
    
    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private async Task<Guid> GetDefaultTenantIdAsync()
    {
        try
        {
            AddDebugInfo("正在获取默认租户...");
            
            // 从 API 获取默认租户
            var tenant = await AuthService.GetDefaultTenantAsync();
            
            if (tenant != null)
            {
                AddDebugInfo($"获取到默认租户: {tenant.Name} ({tenant.Id})");
                return tenant.Id;
            }
            else
            {
                AddDebugInfo("未找到默认租户，使用硬编码的默认值");
                // 由于未能获取默认租户，使用硬编码的默认值
                // 注意：这个值应该与 DataSeeder.cs 中创建的默认租户一致
                // 或者您可以将此值配置在配置文件中
                
                // 尝试第二次获取默认租户，以防第一次是暂时性错误
                AddDebugInfo("尝试第二次获取默认租户...");
                
                try
                {
                    await Task.Delay(500); // 等待500毫秒后重试
                    tenant = await AuthService.GetDefaultTenantAsync();
                    
                    if (tenant != null)
                    {
                        AddDebugInfo($"第二次尝试成功获取到默认租户: {tenant.Name} ({tenant.Id})");
                        return tenant.Id;
                    }
                }
                catch (Exception retryEx)
                {
                    AddDebugInfo($"第二次尝试获取默认租户失败: {retryEx.Message}");
                }
                
                // 如果仍然失败，使用硬编码的值
                var defaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
                AddDebugInfo($"使用硬编码的默认租户ID: {defaultTenantId}");
                return defaultTenantId;
            }
        }
        catch (Exception ex)
        {
            AddDebugInfo($"获取默认租户失败: {ex.Message}");
            Logger.LogError(ex, "获取默认租户ID时发生异常");
            
            // 如果发生异常，使用硬编码的默认值
            var fallbackTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            AddDebugInfo($"使用硬编码的回退租户ID: {fallbackTenantId}");
            return fallbackTenantId;
        }
    }
} 
{"format": 1, "restore": {"E:\\Project-SaaS\\MolySite\\MolySite.Web\\MolySite.Web.csproj": {}}, "projects": {"E:\\Project-SaaS\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj", "projectName": "MolySite.Shared", "projectPath": "E:\\Project-SaaS\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS\\MolySite\\MolySite.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Project-SaaS\\MolySite\\MolySite.Web\\MolySite.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\MolySite.Web.csproj", "projectName": "MolySite.Web", "projectPath": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\MolySite.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\Project-SaaS\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj": {"projectPath": "E:\\Project-SaaS\\MolySite\\MolySite.Shared\\MolySite.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.Components.Analyzers": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.6, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}
@page "/dashboard/superadmin/tenants"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Index> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>租户管理 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">租户管理</h1>
        <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/superadmin/tenants/create"))" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
            创建租户
        </button>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_tenants == null || !_tenants.Any())
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">暂无租户数据</p>
        </div>
    }
    else
    {
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            名称
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            域名
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            订阅计划
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            到期时间
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var tenant in _tenants)
                    {
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">@tenant.Name</div>
                                <div class="text-sm text-gray-500">@(string.IsNullOrEmpty(tenant.Description) ? "-" : tenant.Description)</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">@tenant.Domain</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (tenant.IsActive)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        活跃
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        禁用
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @tenant.SubscriptionPlan
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @(tenant.SubscriptionExpiresAt?.ToString("yyyy-MM-dd") ?? "-")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button @onclick="@(() => NavigationManager.NavigateTo($"/dashboard/superadmin/tenants/edit/{tenant.Id}"))" 
                                            class="text-indigo-600 hover:text-indigo-900">
                                        编辑
                                    </button>
                                    <button @onclick="@(() => NavigationManager.NavigateTo($"/dashboard/superadmin/tenants/website-config/{tenant.Id}"))" 
                                            class="text-green-600 hover:text-green-900">
                                        网站配置
                                    </button>
                                    <button @onclick="@(() => ShowDeleteConfirmation(tenant))" 
                                            class="text-red-600 hover:text-red-900">
                                        删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }

    @if (_showDeleteModal)
    {
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
            <div class="bg-white p-5 rounded-lg shadow-lg max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
                <p class="text-gray-600 mb-6">
                    您确定要删除租户 "@_tenantToDelete?.Name" 吗？此操作无法撤销。
                </p>
                <div class="flex justify-end space-x-3">
                    <button @onclick="CancelDelete" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                        取消
                    </button>
                    <button @onclick="ConfirmDelete" 
                            class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded">
                        删除
                    </button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<TenantDto> _tenants = new();
    private bool _loading = true;
    private bool _showDeleteModal = false;
    private TenantDto? _tenantToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadTenants();
    }

    private async Task LoadTenants()
    {
        try
        {
            _loading = true;
            _tenants = await TenantService.GetAllTenantsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载租户列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private void ShowDeleteConfirmation(TenantDto tenant)
    {
        _tenantToDelete = tenant;
        _showDeleteModal = true;
    }

    private void CancelDelete()
    {
        _showDeleteModal = false;
        _tenantToDelete = null;
    }

    private async Task ConfirmDelete()
    {
        if (_tenantToDelete != null)
        {
            try
            {
                await TenantService.DeleteTenantAsync(_tenantToDelete.Id);
                _showDeleteModal = false;
                _tenantToDelete = null;
                await LoadTenants();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除租户时发生错误");
            }
        }
    }
} 
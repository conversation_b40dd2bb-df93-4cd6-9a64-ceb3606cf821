@page "/dashboard/admin/users"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject IUserService UserService
@inject NavigationManager NavigationManager
@inject ILogger<Index> Logger

<PageTitle>租户用户管理 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">租户用户管理</h1>
        <div class="flex space-x-2">
            <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/admin"))" 
                    class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded">
                返回仪表盘
            </button>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_users == null || !_users.Any())
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">暂无用户数据</p>
            <p class="text-gray-500 text-sm mt-2">您管理的租户中还没有用户</p>
        </div>
    }
    else
    {
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:p-6">
                <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-4 sm:mb-0">
                        <input @bind="_searchTerm" @onkeyup="HandleSearch" 
                               placeholder="搜索用户..." 
                               class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" />
                    </div>
                    <div class="flex space-x-2">
                        <select @bind="_selectedRole" @bind:after="HandleRoleFilter"
                                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                            <option value="">所有角色</option>
                            <option value="TenantAdmin">租户管理员</option>
                            <option value="TenantUser">租户用户</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <ul class="divide-y divide-gray-200">
                @foreach (var user in _filteredUsers)
                {
                    <li>
                        <div class="px-4 py-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-gray-600 font-medium text-sm">
                                            @(string.IsNullOrEmpty(user.FullName) ? user.UserName.Substring(0, 1).ToUpper() : user.FullName.Substring(0, 1).ToUpper())
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">@user.UserName</div>
                                    <div class="text-sm text-gray-500">@user.Email</div>
                                    <div class="text-xs text-gray-400">
                                        @if (!string.IsNullOrEmpty(user.TenantName))
                                        {
                                            <span>租户: @user.TenantName</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                       @(user.Role == "TenantAdmin" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800")">
                                    @GetRoleDisplayName(user.Role)
                                </span>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                       @(user.IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800")">
                                    @(user.IsActive ? "活跃" : "停用")
                                </span>
                            </div>
                        </div>
                    </li>
                }
            </ul>
        </div>
    }
</div>

@code {
    private List<UserDto> _users = new();
    private List<UserDto> _filteredUsers = new();
    private bool _loading = true;
    private string _searchTerm = "";
    private string _selectedRole = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            _loading = true;
            // 这里应该调用获取当前管理员负责的租户用户的API
            // 暂时使用获取所有用户的方法，实际应用中需要根据当前用户权限过滤
            _users = await UserService.GetAllUsersAsync();
            
            // 过滤掉SuperAdmin和Admin角色的用户，只显示租户用户
            _users = _users.Where(u => u.Role == "TenantAdmin" || u.Role == "TenantUser").ToList();
            
            FilterUsers();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载用户列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private void FilterUsers()
    {
        _filteredUsers = _users.Where(u => 
            (string.IsNullOrEmpty(_searchTerm) || 
             u.UserName.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) || 
             u.Email.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) || 
             (u.FullName != null && u.FullName.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase))) &&
            (string.IsNullOrEmpty(_selectedRole) || u.Role == _selectedRole)
        ).ToList();
    }

    private void HandleSearch()
    {
        FilterUsers();
    }

    private void HandleRoleFilter()
    {
        FilterUsers();
        StateHasChanged();
    }

    private string GetRoleDisplayName(string role)
    {
        return role switch
        {
            "TenantAdmin" => "租户管理员",
            "TenantUser" => "租户用户",
            _ => role
        };
    }
}

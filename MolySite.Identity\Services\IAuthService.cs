using System;
using System.Threading.Tasks;
using MolySite.Identity.Models;
using MolySite.Identity.Dtos;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 多租户认证服务接口
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerDto">注册信息</param>
        /// <returns>注册结果</returns>
        Task<AuthResult> RegisterAsync(RegisterDto registerDto);

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果</returns>
        Task<AuthResult> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>刷新令牌结果</returns>
        Task<AuthResult> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// 验证令牌
        /// </summary>
        /// <param name="token">JWT令牌</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidateTokenAsync(string token);

        /// <summary>
        /// 注销
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>注销结果</returns>
        Task LogoutAsync(Guid userId);

        /// <summary>
        /// 获取默认租户
        /// </summary>
        /// <returns>默认租户信息</returns>
        Task<Tenant?> GetDefaultTenantAsync();
    }

    /// <summary>
    /// 认证操作结果
    /// </summary>
    public class AuthResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 登录响应
        /// </summary>
        public LoginResponseDto? LoginResponse { get; set; }
    }
} 
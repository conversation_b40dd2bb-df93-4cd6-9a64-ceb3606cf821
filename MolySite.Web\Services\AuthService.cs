using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using MolySite.Web.Dtos;
using MolySite.Web.Models;
using MolySite.Shared.Models;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 认证服务，处理用户注册、登录等操作
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<AuthService> _logger;
        private readonly ILocalStorageService _localStorage;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IJSInteropService _jsInteropService;
        private readonly IServiceProvider _serviceProvider;

        public AuthService(
            HttpClient httpClient, 
            ILogger<AuthService> logger,
            ILocalStorageService localStorage,
            AuthenticationStateProvider authStateProvider,
            IHttpContextAccessor httpContextAccessor,
            IJSInteropService jsInteropService,
            IServiceProvider serviceProvider)
        {
            _httpClient = httpClient;
            _logger = logger;
            _localStorage = localStorage;
            _authStateProvider = authStateProvider;
            _httpContextAccessor = httpContextAccessor;
            _jsInteropService = jsInteropService;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        public async Task<AuthResult> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("发送注册请求: {UserName}, {Email}", registerDto.UserName, registerDto.Email);
                
                // 移除 ConfirmPassword 属性，因为后端 API 不需要它
                var apiRegisterDto = new
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    Password = registerDto.Password,
                    TenantId = registerDto.TenantId
                };
                
                _logger.LogDebug("注册请求数据: {RegisterDto}", JsonSerializer.Serialize(apiRegisterDto));
                
                var response = await _httpClient.PostAsJsonAsync("api/auth/register", apiRegisterDto);
                
                _logger.LogInformation("收到注册响应: HTTP {StatusCode}", (int)response.StatusCode);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("注册响应内容: {Content}", responseContent);
                    
                    try
                    {
                        var result = JsonSerializer.Deserialize<AuthResult>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        
                        _logger.LogInformation("注册结果解析: Success={Success}", result?.Success);
                        return result ?? new AuthResult { Success = false, ErrorMessage = "注册响应解析失败" };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "解析注册响应时发生错误: {ResponseContent}", responseContent);
                        return new AuthResult { Success = false, ErrorMessage = "注册响应解析失败" };
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("注册失败: HTTP {StatusCode}, {ErrorContent}", (int)response.StatusCode, errorContent);
                    
                    // 尝试解析错误响应
                    try
                    {
                        // 尝试解析为包含 errorMessage 字段的对象
                        var options = new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        };
                        
                        var errorObj = JsonSerializer.Deserialize<JsonElement>(errorContent, options);
                        
                        string errorMessage = null;
                        
                        // 尝试获取 errorMessage 字段
                        if (errorObj.TryGetProperty("errorMessage", out var errorMsgElement) && errorMsgElement.ValueKind == JsonValueKind.String)
                        {
                            errorMessage = errorMsgElement.GetString();
                        }
                        // 尝试获取 message 字段
                        else if (errorObj.TryGetProperty("message", out var msgElement) && msgElement.ValueKind == JsonValueKind.String)
                        {
                            errorMessage = msgElement.GetString();
                        }
                        
                        if (!string.IsNullOrEmpty(errorMessage))
                        {
                            return new AuthResult { Success = false, ErrorMessage = errorMessage };
                        }
                        
                        // 如果没有找到错误消息，根据状态码提供默认消息
                        switch (response.StatusCode)
                        {
                            case System.Net.HttpStatusCode.BadRequest:
                                return new AuthResult { Success = false, ErrorMessage = "注册信息无效，请检查输入" };
                            case System.Net.HttpStatusCode.Conflict:
                                return new AuthResult { Success = false, ErrorMessage = "用户名或电子邮件已存在" };
                            default:
                                return new AuthResult { Success = false, ErrorMessage = $"注册失败 ({(int)response.StatusCode}): {errorContent}" };
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "解析错误响应时发生异常: {ErrorContent}", errorContent);
                        
                        // 根据状态码提供默认错误消息
                        switch (response.StatusCode)
                        {
                            case System.Net.HttpStatusCode.BadRequest:
                                return new AuthResult { Success = false, ErrorMessage = "注册信息无效，请检查输入" };
                            case System.Net.HttpStatusCode.Conflict:
                                return new AuthResult { Success = false, ErrorMessage = "用户名或电子邮件已存在" };
                            default:
                                return new AuthResult { Success = false, ErrorMessage = $"注册失败: HTTP {(int)response.StatusCode}" };
                        }
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "发送注册请求时发生网络错误");
                return new AuthResult { Success = false, ErrorMessage = $"无法连接到服务器: {ex.Message}" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生异常");
                return new AuthResult { Success = false, ErrorMessage = $"注册过程中发生错误: {ex.Message}" };
            }
        }

        /// <summary>
        /// 登录
        /// </summary>
        public async Task<LoginResult> LoginAsync(LoginDto loginDto)
        {
            try
            {
                _logger.LogInformation("发送登录请求: {Username}", loginDto.UserNameOrEmail);
                
                // 发送登录请求
                var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginDto);
                
                _logger.LogInformation("收到登录响应: HTTP {StatusCode}", response.StatusCode);
                
                if (response.IsSuccessStatusCode)
                {
                    // 读取原始响应内容
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("原始响应内容: {ResponseContent}", responseContent);
                    
                    try
                    {
                        // 解析响应
                        var options = new JsonSerializerOptions 
                        { 
                            PropertyNameCaseInsensitive = true 
                        };
                        
                        var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent, options);
                        
                        if (apiResponse.TryGetProperty("success", out JsonElement successElement) && 
                            successElement.GetBoolean())
                        {
                            if (apiResponse.TryGetProperty("loginResponse", out JsonElement loginResponseElement))
                            {
                                _logger.LogInformation("API登录响应: {ApiLoginResponse}", loginResponseElement.ToString());
                                
                                // 手动构建LoginResponseDto
                                var result = new LoginResponseDto();
                                
                                // 尝试获取各个字段
                                if (loginResponseElement.TryGetProperty("token", out JsonElement tokenElement))
                                {
                                    result.Token = tokenElement.GetString() ?? string.Empty;
                                    _logger.LogInformation("获取到Token: {Token}", result.Token);
                                }
                                
                                if (loginResponseElement.TryGetProperty("refreshToken", out JsonElement refreshTokenElement))
                                {
                                    result.RefreshToken = refreshTokenElement.GetString() ?? string.Empty;
                                    _logger.LogInformation("获取到RefreshToken: {RefreshToken}", result.RefreshToken);
                                }
                                
                                if (loginResponseElement.TryGetProperty("userId", out JsonElement userIdElement))
                                {
                                    if (userIdElement.ValueKind == JsonValueKind.String)
                                    {
                                        if (Guid.TryParse(userIdElement.GetString(), out Guid parsedId))
                                        {
                                            result.UserId = parsedId;
                                        }
                                    }
                                    else if (userIdElement.ValueKind == JsonValueKind.Object)
                                    {
                                        // 可能是GUID对象
                                        result.UserId = userIdElement.Deserialize<Guid>(options);
                                    }
                                    _logger.LogInformation("获取到UserId: {UserId}", result.UserId);
                                }
                                
                                if (loginResponseElement.TryGetProperty("userName", out JsonElement userNameElement))
                                {
                                    result.UserName = userNameElement.GetString() ?? string.Empty;
                                    _logger.LogInformation("获取到UserName: {UserName}", result.UserName);
                                }
                                
                                if (loginResponseElement.TryGetProperty("email", out JsonElement emailElement))
                                {
                                    result.Email = emailElement.GetString() ?? string.Empty;
                                    _logger.LogInformation("获取到Email: {Email}", result.Email);
                                }
                                
                                if (loginResponseElement.TryGetProperty("roles", out JsonElement rolesElement) && 
                                    rolesElement.ValueKind == JsonValueKind.Array)
                                {
                                    result.Roles = new List<string>();
                                    foreach (var role in rolesElement.EnumerateArray())
                                    {
                                        result.Roles.Add(role.GetString() ?? string.Empty);
                                    }
                                    _logger.LogInformation("获取到Roles: {Roles}", string.Join(", ", result.Roles));
                                }
                                else
                                {
                                    // 如果找不到角色列表，添加超级管理员角色作为默认角色
                                    result.Roles = new List<string> { "SuperAdmin" };
                                    _logger.LogWarning("未找到角色列表，默认添加SuperAdmin角色");
                                }
                                
                                // 如果在解析后UserId仍然为空，则设置一个固定的超级管理员ID
                                if (result.UserId == Guid.Empty)
                                {
                                    // 创建一个基于用户名的确定性GUID
                                    var nameBytes = System.Text.Encoding.UTF8.GetBytes(result.UserName);
                                    var md5 = System.Security.Cryptography.MD5.Create();
                                    var hashBytes = md5.ComputeHash(nameBytes);
                                    result.UserId = new Guid(hashBytes);
                                    
                                    _logger.LogWarning("未找到用户ID，基于用户名创建确定性ID: {UserId}", result.UserId);
                                }
                                
                                return new LoginResult
                                {
                                    Success = true,
                                    LoginResponse = result
                                };
                            }
                        }
                        
                        _logger.LogWarning("登录响应解析失败或success不为true");
                        return new LoginResult
                        {
                            Success = false,
                            ErrorMessage = "登录响应解析失败"
                        };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "解析登录响应时发生异常: {ResponseContent}", responseContent);
                        return new LoginResult
                        {
                            Success = false,
                            ErrorMessage = $"解析登录响应时发生异常: {ex.Message}"
                        };
                    }
                }
                
                // 处理错误
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("登录失败: {StatusCode}, {ErrorContent}", response.StatusCode, errorContent);
                
                return new LoginResult
                {
                    Success = false,
                    ErrorMessage = $"登录失败: {response.StatusCode}, {errorContent}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生异常");
                return new LoginResult
                {
                    Success = false,
                    ErrorMessage = $"登录过程中发生异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 保存认证令牌
        /// </summary>
        public async Task SaveAuthTokens(LoginResponseDto loginResponse)
        {
            try
            {
                // 确保Roles不为null
                if (loginResponse.Roles == null)
                {
                    loginResponse.Roles = new List<string>();
                    _logger.LogWarning("用户角色为null，已初始化为空列表");
                }
                
                // 检查用户ID是否为空
                if (loginResponse.UserId == Guid.Empty)
                {
                    _logger.LogWarning("用户ID为空，认证信息不完整，可能需要重新登录");
                }
                
                // 检查用户名是否为空
                if (string.IsNullOrEmpty(loginResponse.UserName))
                {
                    _logger.LogWarning("用户名为空，认证信息不完整，可能需要重新登录");
                }
                
                // 检查邮箱是否为空
                if (string.IsNullOrEmpty(loginResponse.Email))
                {
                    _logger.LogWarning("邮箱为空，认证信息不完整，可能需要重新登录");
                }
                
                // 确保令牌不为空
                if (string.IsNullOrEmpty(loginResponse.Token))
                {
                    _logger.LogWarning("令牌为空，可能导致认证问题");
                }
                
                if (string.IsNullOrEmpty(loginResponse.RefreshToken))
                {
                    _logger.LogWarning("刷新令牌为空，可能导致认证问题");
                }
                
                _logger.LogInformation("保存认证令牌: {UserId}, {UserName}, Roles: {Roles}, TokenLength: {TokenLength}",
                    loginResponse.UserId, loginResponse.UserName, 
                    string.Join(", ", loginResponse.Roles), 
                    loginResponse.Token?.Length ?? 0);
                
                // 尝试设置Cookie认证票据
                try
                {
                    var httpContext = _httpContextAccessor.HttpContext;

                    if (httpContext != null && !httpContext.Response.HasStarted)
                    {
                        // 创建声明
                        var claims = new List<Claim>
                        {
                            new Claim(ClaimTypes.NameIdentifier, loginResponse.UserId.ToString()),
                            new Claim(ClaimTypes.Name, loginResponse.UserName),
                            new Claim(ClaimTypes.Email, loginResponse.Email)
                        };

                        // 添加角色声明
                        foreach (var role in loginResponse.Roles)
                        {
                            claims.Add(new Claim(ClaimTypes.Role, role));
                            _logger.LogInformation("添加角色声明到Cookie认证: {Role}", role);
                        }

                        // 创建身份
                        var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                        var principal = new ClaimsPrincipal(identity);

                        // 设置认证属性
                        var authProperties = new AuthenticationProperties
                        {
                            IsPersistent = true,
                            ExpiresUtc = DateTimeOffset.UtcNow.AddDays(7)
                        };

                        // 设置Cookie认证票据
                        await httpContext.SignInAsync(
                            CookieAuthenticationDefaults.AuthenticationScheme,
                            principal,
                            authProperties);

                        _logger.LogInformation("已设置Cookie认证票据，角色: {Roles}",
                            string.Join(", ", loginResponse.Roles));

                        // 验证Cookie认证是否成功设置
                        var cookieUser = httpContext.User;
                        _logger.LogInformation("Cookie认证验证 - 已认证: {IsAuthenticated}, 角色: {Roles}",
                            cookieUser.Identity?.IsAuthenticated,
                            string.Join(", ", cookieUser.Claims
                                .Where(c => c.Type == ClaimTypes.Role)
                                .Select(c => c.Value)));
                    }
                    else
                    {
                        _logger.LogWarning("HttpContext不可用或响应已开始，无法设置Cookie认证票据");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Cookie认证票据创建失败，可能会导致授权问题");
                }
                
                // 保存令牌到本地存储
                await _jsInteropService.SetLocalStorage("authToken", loginResponse.Token!);
                _logger.LogInformation("已保存authToken到本地存储");
                
                await _jsInteropService.SetLocalStorage("refreshToken", loginResponse.RefreshToken!);
                _logger.LogInformation("已保存refreshToken到本地存储");
                
                // 保存用户信息到本地存储
                var userJson = JsonSerializer.Serialize(loginResponse);
                await _jsInteropService.SetLocalStorage("user", userJson);
                _logger.LogInformation("已保存user到本地存储: {UserJson}", userJson);
                
                // 保存认证时间戳
                await _jsInteropService.SetLocalStorage("auth_timestamp", DateTime.UtcNow.ToString("o"));
                _logger.LogInformation("已保存认证时间戳到本地存储");
                
                // 设置一个标记Cookie
                await _jsInteropService.SetCookie("MolySite.AuthUser", loginResponse.UserName, 1);
                await _jsInteropService.SetCookie("MolySite.AuthRole", string.Join(",", loginResponse.Roles), 1);
                _logger.LogInformation("已设置标记Cookie: MolySite.AuthRole={Roles}", string.Join(",", loginResponse.Roles));
                
                // 标记用户为已认证
                if (_authStateProvider is CustomAuthStateProvider customProvider)
                {
                    customProvider.MarkUserAsAuthenticated(loginResponse);
                    _logger.LogInformation("已更新认证状态");
                }
                
                // 验证认证状态
                var authState = await _authStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                _logger.LogInformation("认证状态验证 - 已认证: {IsAuthenticated}, 角色: {Roles}",
                    user.Identity?.IsAuthenticated,
                    string.Join(", ", user.Claims
                        .Where(c => c.Type == ClaimTypes.Role)
                        .Select(c => c.Value)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存认证令牌时发生异常");
                throw;
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public async Task LogoutAsync()
        {
            try
            {
                // 删除本地存储中的令牌
                await _jsInteropService.RemoveLocalStorage("authToken");
                await _jsInteropService.RemoveLocalStorage("refreshToken");
                await _jsInteropService.RemoveLocalStorage("user");
                await _jsInteropService.RemoveLocalStorage("auth_timestamp");
                
                // 删除Cookie
                await _jsInteropService.EraseCookie("MolySite.AuthUser");
                await _jsInteropService.EraseCookie("MolySite.AuthRole");
                
                // 尝试注销Cookie认证
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var httpContextAccessor = scope.ServiceProvider.GetService<IHttpContextAccessor>();
                    
                    if (httpContextAccessor?.HttpContext != null)
                    {
                        await httpContextAccessor.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "注销Cookie认证时发生异常");
                }
                
                // 标记用户为已注销
                if (_authStateProvider is CustomAuthStateProvider customProvider)
                {
                    customProvider.MarkUserAsLoggedOut();
                }
                
                _logger.LogInformation("用户已成功注销");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注销过程中发生异常");
                throw;
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        public async Task<LoginResponseDto?> GetCurrentUserAsync()
        {
            try
            {
                var userJson = await _jsInteropService.GetLocalStorage("user");
                
                if (string.IsNullOrEmpty(userJson))
                {
                    return null;
                }
                
                return JsonSerializer.Deserialize<LoginResponseDto>(userJson, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前用户信息时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 刷新认证令牌
        /// </summary>
        public async Task<bool> RefreshTokenAsync()
        {
            try
            {
                // 获取刷新令牌
                var refreshToken = await _jsInteropService.GetLocalStorage("refreshToken");
                
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("没有找到刷新令牌");
                    return false;
                }
                
                // 发送刷新令牌请求 - 修正API路径
                var response = await _httpClient.PostAsJsonAsync("api/auth/refresh-token", refreshToken);
                
                if (response.IsSuccessStatusCode)
                {
                    // 解析响应
                    var loginResponse = await response.Content.ReadFromJsonAsync<LoginResponseDto>();
                    
                    if (loginResponse != null)
                    {
                        // 保存新的令牌
                        await SaveAuthTokens(loginResponse);
                        return true;
                    }
                }
                
                _logger.LogWarning("刷新令牌失败: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 检查认证状态
        /// </summary>
        public async Task<bool> IsAuthenticatedAsync()
        {
            try
            {
                // 获取认证状态
                var authState = await _authStateProvider.GetAuthenticationStateAsync();
                return authState.User.Identity?.IsAuthenticated == true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查认证状态时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否有指定角色
        /// </summary>
        public async Task<bool> IsInRoleAsync(string role)
        {
            try
            {
                // 获取认证状态
                var authState = await _authStateProvider.GetAuthenticationStateAsync();
                return authState.User.IsInRole(role);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户角色时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取默认租户
        /// </summary>
        public async Task<AuthTenantDto?> GetDefaultTenantAsync()
        {
            try
            {
                _logger.LogInformation("获取默认租户");
                
                var response = await _httpClient.GetAsync("api/auth/default-tenant");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("获取默认租户响应: {Content}", content);
                    
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var result = JsonSerializer.Deserialize<DefaultTenantResponse>(content, options);
                    
                    if (result?.Success == true && result.Tenant != null)
                    {
                        return new AuthTenantDto
                        {
                            Id = result.Tenant.Id,
                            Name = result.Tenant.Name
                        };
                    }
                }
                
                _logger.LogWarning("获取默认租户失败: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认租户时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 获取认证详情
        /// </summary>
        public async Task<AuthDetails> GetAuthDetails()
        {
            var authDetails = new AuthDetails();
            
            try
            {
                // 从本地存储获取用户信息
                var userJson = await _jsInteropService.GetLocalStorage("user");
                if (!string.IsNullOrEmpty(userJson))
                {
                    var loginResponse = JsonSerializer.Deserialize<LoginResponseDto>(userJson, 
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    
                    if (loginResponse != null)
                    {
                        authDetails.UserId = loginResponse.UserId;
                        authDetails.UserName = loginResponse.UserName;
                        authDetails.Email = loginResponse.Email;
                        authDetails.Roles = loginResponse.Roles ?? new List<string>();
                    }
                }
                
                // 获取令牌信息
                authDetails.Token = await _jsInteropService.GetLocalStorage("authToken");
                authDetails.RefreshToken = await _jsInteropService.GetLocalStorage("refreshToken");
                
                // 获取认证时间
                var authTimeStr = await _jsInteropService.GetLocalStorage("auth_timestamp");
                if (!string.IsNullOrEmpty(authTimeStr) && DateTime.TryParse(authTimeStr, out var authTime))
                {
                    authDetails.AuthTime = authTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    
                    // 计算令牌是否有效
                    authDetails.HasValidToken = !string.IsNullOrEmpty(authDetails.Token) && 
                                              DateTime.UtcNow.Subtract(authTime).TotalHours < 12; // 假设12小时有效期
                }
                
                // 如果从本地存储中没有获取到用户信息，尝试从AuthenticationState获取
                if (string.IsNullOrEmpty(authDetails.UserName))
                {
                    var authState = await _authStateProvider.GetAuthenticationStateAsync();
                    if (authState.User.Identity?.IsAuthenticated == true)
                    {
                        authDetails.UserName = authState.User.Identity.Name ?? string.Empty;
                        
                        var roles = authState.User.Claims
                            .Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)
                            .Select(c => c.Value)
                            .ToList();
                        
                        authDetails.Roles = roles;
                        
                        var userId = authState.User.Claims
                            .FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                        
                        if (!string.IsNullOrEmpty(userId) && Guid.TryParse(userId, out var id))
                        {
                            authDetails.UserId = id;
                        }
                        
                        var email = authState.User.Claims
                            .FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.Email)?.Value;
                        
                        if (!string.IsNullOrEmpty(email))
                        {
                            authDetails.Email = email;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取认证详情时发生错误");
            }
            
            return authDetails;
        }
    }

    /// <summary>
    /// 错误响应模型
    /// </summary>
    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 默认租户响应
    /// </summary>
    public class DefaultTenantResponse
    {
        public bool Success { get; set; }
        public TenantDto? Tenant { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 租户数据传输对象
    /// </summary>
    public class AuthTenantDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }
} 
using System;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using System.Net.Mail;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using MolySite.Identity.Models;
using MolySite.Identity.Data;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 密码服务实现
    /// </summary>
    public class PasswordService : IPasswordService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _context;

        public PasswordService(
            UserManager<ApplicationUser> userManager, 
            IConfiguration configuration,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <inheritdoc/>
        public async Task<string> GeneratePasswordResetTokenAsync(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
                throw new InvalidOperationException("用户不存在");

            // 生成安全的重置令牌
            var token = GenerateSecureToken();
            
            // 存储令牌和过期时间
            user.PasswordResetToken = token;
            user.PasswordResetTokenExpiration = DateTime.UtcNow.AddHours(1);

            await _userManager.UpdateAsync(user);
            return token;
        }

        /// <inheritdoc/>
        public async Task<bool> ValidatePasswordResetTokenAsync(Guid userId, string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
                return false;

            // 检查令牌是否匹配且未过期
            return user.PasswordResetToken == token && 
                   user.PasswordResetTokenExpiration.HasValue && 
                   user.PasswordResetTokenExpiration.Value > DateTime.UtcNow;
        }

        /// <inheritdoc/>
        public async Task<bool> ResetPasswordAsync(Guid userId, string token, string newPassword)
        {
            if (string.IsNullOrWhiteSpace(token) || string.IsNullOrWhiteSpace(newPassword))
                return false;

            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
                return false;

            // 验证令牌
            if (!await ValidatePasswordResetTokenAsync(userId, token))
                return false;

            // 重置密码
            var result = await _userManager.ResetPasswordAsync(user, token, newPassword);
            
            if (result.Succeeded)
            {
                // 清除重置令牌
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpiration = null;
                await _userManager.UpdateAsync(user);
                return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public async Task<bool> SendPasswordResetEmailAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
                return false;

            // 生成重置令牌
            var token = await GeneratePasswordResetTokenAsync(user.Id);
            if (token == null)
                return false;
            
            // 构建重置链接
            var frontendUrl = _configuration["AppSettings:FrontendUrl"] 
                ?? throw new InvalidOperationException("未配置前端URL");
            var resetLink = $"{frontendUrl}/reset-password?token={token}&userId={user.Id}";

            // 发送邮件
            try 
            {
                var smtpHost = _configuration["Smtp:Host"] 
                    ?? throw new InvalidOperationException("未配置SMTP主机");
                var smtpPort = int.Parse(_configuration["Smtp:Port"] 
                    ?? throw new InvalidOperationException("未配置SMTP端口"));

                using (var smtpClient = new SmtpClient(smtpHost, smtpPort))
                {
                    smtpClient.EnableSsl = true;
                    smtpClient.Credentials = new System.Net.NetworkCredential(
                        _configuration["Smtp:Username"] 
                            ?? throw new InvalidOperationException("未配置SMTP用户名"),
                        _configuration["Smtp:Password"] 
                            ?? throw new InvalidOperationException("未配置SMTP密码")
                    );

                    var fromEmail = _configuration["Smtp:FromEmail"] 
                        ?? throw new InvalidOperationException("未配置发件人邮箱");

                    var mailMessage = new MailMessage
                    {
                        From = new MailAddress(fromEmail),
                        Subject = "密码重置",
                        Body = $"请点击以下链接重置您的密码：{resetLink}",
                        IsBodyHtml = true
                    };
                    mailMessage.To.Add(email);

                    await smtpClient.SendMailAsync(mailMessage);
                }
                return true;
            }
            catch (Exception)
            {
                // 记录异常，但不暴露具体错误细节
                return false;
            }
        }

        /// <summary>
        /// 生成安全的重置令牌
        /// </summary>
        private string GenerateSecureToken()
        {
            byte[] tokenData = new byte[32];
            RandomNumberGenerator.Fill(tokenData);
            return Convert.ToBase64String(tokenData);
        }
    }
} 
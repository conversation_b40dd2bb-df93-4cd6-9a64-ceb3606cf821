using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 授权服务接口
    /// </summary>
    public interface IAuthorizationService
    {
        /// <summary>
        /// 添加角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> AddRoleAsync(string roleName, Guid tenantId);

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> RemoveRoleAsync(string roleName, Guid tenantId);

        /// <summary>
        /// 为角色分配权限
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <param name="permissions">权限列表</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> AssignPermissionsToRoleAsync(string roleName, List<string> permissions, Guid tenantId);

        /// <summary>
        /// 获取角色权限
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>权限列表</returns>
        Task<List<string>> GetRolePermissionsAsync(string roleName, Guid tenantId);

        /// <summary>
        /// 检查用户是否具有指定权限
        /// </summary>
        /// <param name="user">用户的 ClaimsPrincipal</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否具有权限</returns>
        Task<bool> HasPermissionAsync(ClaimsPrincipal user, string permission);

        /// <summary>
        /// 检查用户是否具有指定权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permission">权限名称</param>
        /// <returns>是否具有权限</returns>
        Task<bool> HasPermissionAsync(Guid userId, string permission);

        /// <summary>
        /// 获取用户的所有权限
        /// </summary>
        /// <param name="user">当前用户</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<string>> GetUserPermissionsAsync(ClaimsPrincipal user);

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleName">角色名称</param>
        /// <returns>是否成功</returns>
        Task<bool> AssignRoleToUserAsync(Guid userId, string roleName);

        /// <summary>
        /// 为角色添加权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="permissions">权限列表</param>
        /// <returns>是否成功添加权限</returns>
        Task<bool> AddRolePermissionsAsync(Guid roleId, Guid tenantId, string[] permissions);
    }
} 
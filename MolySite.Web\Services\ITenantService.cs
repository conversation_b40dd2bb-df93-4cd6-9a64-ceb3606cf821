using MolySite.Shared.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 租户服务客户端接口
    /// </summary>
    public interface ITenantService
    {
        /// <summary>
        /// 获取所有租户（仅限SuperAdmin）
        /// </summary>
        Task<List<TenantDto>> GetAllTenantsAsync();

        /// <summary>
        /// 获取指定租户
        /// </summary>
        Task<TenantDto> GetTenantByIdAsync(Guid id);

        /// <summary>
        /// 获取当前用户的租户
        /// </summary>
        Task<List<TenantDto>> GetMyTenantsAsync();

        /// <summary>
        /// 创建租户
        /// </summary>
        Task<TenantDto> CreateTenantAsync(TenantDto tenant);

        /// <summary>
        /// 更新租户
        /// </summary>
        Task<bool> UpdateTenantAsync(TenantDto tenant);

        /// <summary>
        /// 删除租户（仅限SuperAdmin）
        /// </summary>
        Task<bool> DeleteTenantAsync(Guid id);

        /// <summary>
        /// 获取租户的网站配置
        /// </summary>
        Task<WebsiteConfigDto> GetWebsiteConfigAsync(Guid tenantId);

        /// <summary>
        /// 更新租户的网站配置
        /// </summary>
        Task<bool> UpdateWebsiteConfigAsync(WebsiteConfigDto config);

        /// <summary>
        /// 获取所有订阅计划
        /// </summary>
        Task<List<SubscriptionPlanDto>> GetAllSubscriptionPlansAsync();

        /// <summary>
        /// 获取指定订阅计划
        /// </summary>
        Task<SubscriptionPlanDto> GetSubscriptionPlanByIdAsync(Guid id);

        /// <summary>
        /// 创建订阅计划（仅限SuperAdmin）
        /// </summary>
        Task<SubscriptionPlanDto> CreateSubscriptionPlanAsync(SubscriptionPlanDto plan);

        /// <summary>
        /// 更新订阅计划（仅限SuperAdmin）
        /// </summary>
        Task<bool> UpdateSubscriptionPlanAsync(SubscriptionPlanDto plan);

        /// <summary>
        /// 删除订阅计划（仅限SuperAdmin）
        /// </summary>
        Task<bool> DeleteSubscriptionPlanAsync(Guid id);

        /// <summary>
        /// 更新租户的订阅计划（仅限SuperAdmin）
        /// </summary>
        Task<bool> UpdateTenantSubscriptionAsync(Guid tenantId, string planName, DateTime? expiryDate);

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        Task<bool> CheckDomainAvailabilityAsync(string domain, Guid? excludeTenantId = null);
    }
}

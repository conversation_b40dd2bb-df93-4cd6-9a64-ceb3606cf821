// 辅助JavaScript函数，用于与Blazor交互

// 获取当前URL
function getCurrentUrl() {
    return window.location.href;
}

// 确保所有基本JS互操作方法可用
window.blazorHelpers = {
    // 浏览器导航相关
    getCurrentUrl: getCurrentUrl,
    
    // Cookie相关
    getCookie: function(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return '';
    },
    
    // 错误处理
    logError: function(message) {
        console.error(`[Blazor JS Error]: ${message}`);
    }
}; 
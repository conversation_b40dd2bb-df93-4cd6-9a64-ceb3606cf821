{"ConnectionStrings": {"DefaultConnection": "Data Source=./Data/MolySite.db"}, "Jwt": {"Issuer": "MolySite", "Audience": "MolySiteUsers", "Key": "ThisIsAVeryLongAndSecureSecretKeyForJwtTokenGeneration", "ExpirationInMinutes": 60}, "Smtp": {"Host": "smtp.example.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-email-password", "FromEmail": "<EMAIL>"}, "AppSettings": {"FrontendUrl": "https://localhost:3000"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}
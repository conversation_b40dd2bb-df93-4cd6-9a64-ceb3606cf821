{"GlobalPropertiesHash": "XQMmMgi/nUTAZDKg3OqM/JAU+0imFt9GMdGS3A72IP8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["C0m0xntIgEj71/8Bq+1grhWriK+/bAtN5KHBnXYV3/s=", "J0k2tRFGAfcPH2MsLxPVTXnQYtyktNDVAEpnWaFC7DA=", "Yma7AyUbV7m/3ZkZ7UIKIbZti2Vyh9wNFh++05mgv/E=", "PBCx0UwqbFsT1ZxNXiVJy6xLrKmutovX057fjrniWWo=", "ItldWqH0OY6J1zYffZeyCSCLQe2Uu6tPuAs3bcmADAM=", "a00Qr/eOMWcn1xSxCiHwG17xMyKg2EaK59o1y4TdtX8=", "2wrsc2BbbYy3juZ2veZsGCgit2SZarsOgwmy4Eiv70U=", "yrHnaS1abumiN23cl85OphGvPivOZRbalblo4b/ggCE=", "o6OevsmoH3g7CMvS/hP+7vJqcfkWRWltiUK3By2TNcE=", "ENuoIT4SW+yugsviANtPpOTmBDYcDGSWjo/WS1eXabA=", "eZchIqR/rs+yAF9vExJQb86CujUhbBKrSfE66ABMxNk=", "jaYs4hxio5iBJrRjOHJcxXg4QHkRO4BEiliHChlLp4g=", "vg54V24kT7GO/rLYXrILQKV2M8W5ZLFXBCP5lVOOt9I=", "QReDmfwFL+gIIAbk17KhWGEYUUev7JM+gVda4+FZJMQ=", "n7EX/vcNbIPvLFPUJPOpKxqzKO18GAAiwPrhxnz4iv8=", "5soaZJkzda2wJNzERScDgqTgB0GGUVWPYB3BO47/+Us=", "bdrtiR82dxle/2nDu12XZhEN71GAlRAW4gcQxQnhaM4=", "wmFKcw10j2qm7CETvJVy6HIJgenXu0Py3XZgKtavi4o=", "6dm9rvT/FW7F8ry1RfXgJcf2+6cPoi7LcbW7LGmVi48=", "G+6tTYdMoipMygLP3gzqLoD8x0CL812xjT/o+cZvwu4=", "JA9My28QNKXQtW1Eiiak8flqA1TTnelZSWqgNiTyfIw=", "k1F3XF9CBZHHOJ67Mb4yf6eMVCoHauMT805guVKng7w=", "bwYnzLeZ7pdP52+vdpjWWMvsk8ZtZAV5msOh8srzs4k=", "lnM8rE3W+oEna5bNt9ZUksS2vVV57tayRSTn4oeeB44=", "iz+7Qx6m1ZPlC1GP8XV+//sCLChEhefjCJmPAt+gKyY=", "0WHfgDZUX5Z93XSj4BifiquXwjIme2g5Y5FgFQXuShA=", "dG2EfOEsQQL6T6NzFFoQVYIULjdNI+Q/pqPZDsD3+DY=", "vm5QMHD9L8hvjETaL1fenu1NKc+1HetdvAYAQEy2Z28=", "k2fOeP3Ubt887jY1xHe4MJktjv23MnvxpIlhlW5Izv4=", "IkMtfjFhasTlD48dCQlFPo1J5NOt2saPLegbtSMzPZ0=", "vbALdn76JZfx27BjvzGji9fvXpyD2CJ0lQgSl2FvusI=", "9W3lpu3zVz57igqwAYQbZAyTUlXSiIB7uOAqcRQyDac=", "iIVZ1+Wg5LP6g/ifoYg8DIE29VsgdmCxAg3d280G0Ig=", "MAsvu60f9oYPK9TXgImLgYRfZ7oOqYHpPIUqdrRaGqw=", "DtzP29g9DRfFaBGVoVJmOQ4mTyozs085BSjmXxh9pgE=", "ZXuAhYhFMSYUgeH6A18qUnIsJIDoiqNwv+OsCy/kRUQ=", "Q29HjDxgPO32FCrQlkFNMAm7x47DQVJUsbj5J9w1PG8=", "HljOV8vX/DbRMoKJpP8HR8V+5oD/qR6YUbapdytGQkI=", "E+WT+11zdDK4K37tj0B2FWNteDXlC5JriKqQx6VlXhw=", "Dj6ZIwpCegOZT1sPZC5mJpFccozkk7SQuyb0MJlslXA=", "lq/E4+GcESPt62aYuqAErYyzWBUtzeNCSMba2yCP+hM=", "43scEa3j2XJkQ6N1L8WctiSdJ9qJ9eqN/NU1jE1NyTA=", "DIYh3VvRuEs3DUuPdRzV9qlLe5gyDqfxc8Ww3j2XH3s=", "GYDqaX8I2Hwp0O0v6rAo8l/01UCmA98mLJAALBOeSPw=", "vOzjcVgVQ6ppQZVvWDOALw90Mz7LGJy7BKHNREY3pcQ=", "CcVsHLMAHtJwJzimZeYtgLnUBWTKlkzUzM7mvW/yxpg=", "Iy7EsJ8l0V+Id0x8NGiVmM7SpLOMzL+CYTi0B/PKJ4Y=", "5fO1X5HSFyycNBuxKo5I+BYoFR4oUV3SCjPe9OGXOKk=", "4AbfCGBVeHrs/i01MNUDylA4bDXuAxDJBpUYaQiucsM=", "agunYtkkEukYPfLk9DMgqR+J4lGOr4dOWco1nlMlCs8=", "7VhUdL976tExUoxHcMozW7nkX5WLOl1L8dsZKc7cFUk=", "keujobwBWUqmmeTfJjAJAya2+SIvsBU05HZnJ6uzlqY=", "eAU8D75ZhA8DwDcvSHn7ROW9kFcEjdYQTbXeVCVSKYo=", "nz9JjRP/Iq9G5gNgxT0rPp7rXenpFa1WVcYEhATa1A0=", "nnkGz1vDFzy3614bbVn5YFxNHIGgPvUliyGldQSbppI=", "cwPZ1rRbst+Ka3LL8t6MpXASLfoByOKo4VcoF5Al/Ok=", "o3li8PmZ8OHTD2ALd7CkARrmNREC5V3lv61S/HJKFxY=", "+phtNGBw0sonvf+RTKCwVgUVR/oONPY2UZkgZ7u8GLw=", "YsFO1LZls3/UqqBADpYIKgyhJMH6si+eemXLjayohgk=", "2Ux2CWa4wkO8AXORpwj0hnL2omLUvum6s9A7mEYUHto=", "UYOGia7ppiFaFGZEimpX259TmIWgA1+7uTsfTQS7/Ws=", "v3ZrfUiz7JWz9KSuw1C1ziFq3vFbOxVTm83Lqx3sYGE=", "K2lbAxdL7pSHPVgWdFIRfQ6dSfMulRz5lbSo3xIqzHE=", "/sHdXjXgU83TZnXX/Yebf8h4P9LBmLI8RY1uefcUwaY=", "+stu6WRVmkGbuzhY4tn8nkusgfDnlqloq13iGZih0Xo=", "85VWBKEvtIa0OpEAq8f6hj3OXk1+aU4hlwwURPDU+ok=", "ofFZuW0E86D4f1ChYtBvvgB6/mdA5c2zQWdBl8pI0k0=", "50ClO01IqpttUYV1K60ifudPqnLRiR6ICCuQ3hYt/rs=", "ywGPJ7CF0kMM8/bx1jaSmulOZe6ePGb8qFokzitfEMM=", "AOqLWm4hiba1OtjX+ilB/1EXvY0GN2Vv9NyLSTDJwJI=", "uBUoOe3eVBU4VYosA+SHr7+NlthmkXn6McPWqFT49Tc=", "UPzYi/tafqrnincZKMzRVey3zVDuewWgSdVi4xIZtKg=", "XuqageGF5toizg9ysqX7eAn/Y+DhTH7bvhQLkAFObpI=", "xZvts8JEHwaQZNnQmYeEjPRV8Az6Vl195azaAODTPK8=", "vQSz1JemS8WQvNVHahNvbVJQr8h4sCRkm9ZE63AayfE=", "gc/hj+OhIIUU4Pc3MsXrnLKJn8rEqUqL3K6gdOjiZzw=", "fj5CLwRHQJ2CLB4g+swgs+yuEYrLoL91GaXIrpnYNII=", "LbgomMK8AAQaZn9181626Wpn9PF3CjVWYy4ollOir/w=", "9hd/m3Rs3vlHrD79hFUSwYvgHGLVJuB+W3g0atMFo0o=", "rtE2ZN2DZErXlbijxBAADYhWnkrsBzjQYXjsL3XT+Xw="], "CachedAssets": {"5fO1X5HSFyycNBuxKo5I+BYoFR4oUV3SCjPe9OGXOKk=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "Iy7EsJ8l0V+Id0x8NGiVmM7SpLOMzL+CYTi0B/PKJ4Y=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "CcVsHLMAHtJwJzimZeYtgLnUBWTKlkzUzM7mvW/yxpg=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "vOzjcVgVQ6ppQZVvWDOALw90Mz7LGJy7BKHNREY3pcQ=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "GYDqaX8I2Hwp0O0v6rAo8l/01UCmA98mLJAALBOeSPw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "DIYh3VvRuEs3DUuPdRzV9qlLe5gyDqfxc8Ww3j2XH3s=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "43scEa3j2XJkQ6N1L8WctiSdJ9qJ9eqN/NU1jE1NyTA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "lq/E4+GcESPt62aYuqAErYyzWBUtzeNCSMba2yCP+hM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "Dj6ZIwpCegOZT1sPZC5mJpFccozkk7SQuyb0MJlslXA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "E+WT+11zdDK4K37tj0B2FWNteDXlC5JriKqQx6VlXhw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "HljOV8vX/DbRMoKJpP8HR8V+5oD/qR6YUbapdytGQkI=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "Q29HjDxgPO32FCrQlkFNMAm7x47DQVJUsbj5J9w1PG8=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "ZXuAhYhFMSYUgeH6A18qUnIsJIDoiqNwv+OsCy/kRUQ=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "DtzP29g9DRfFaBGVoVJmOQ4mTyozs085BSjmXxh9pgE=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "MAsvu60f9oYPK9TXgImLgYRfZ7oOqYHpPIUqdrRaGqw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "iIVZ1+Wg5LP6g/ifoYg8DIE29VsgdmCxAg3d280G0Ig=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "9W3lpu3zVz57igqwAYQbZAyTUlXSiIB7uOAqcRQyDac=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "vbALdn76JZfx27BjvzGji9fvXpyD2CJ0lQgSl2FvusI=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "IkMtfjFhasTlD48dCQlFPo1J5NOt2saPLegbtSMzPZ0=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "k2fOeP3Ubt887jY1xHe4MJktjv23MnvxpIlhlW5Izv4=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "vm5QMHD9L8hvjETaL1fenu1NKc+1HetdvAYAQEy2Z28=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "dG2EfOEsQQL6T6NzFFoQVYIULjdNI+Q/pqPZDsD3+DY=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "0WHfgDZUX5Z93XSj4BifiquXwjIme2g5Y5FgFQXuShA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "iz+7Qx6m1ZPlC1GP8XV+//sCLChEhefjCJmPAt+gKyY=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "lnM8rE3W+oEna5bNt9ZUksS2vVV57tayRSTn4oeeB44=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "bwYnzLeZ7pdP52+vdpjWWMvsk8ZtZAV5msOh8srzs4k=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "k1F3XF9CBZHHOJ67Mb4yf6eMVCoHauMT805guVKng7w=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "JA9My28QNKXQtW1Eiiak8flqA1TTnelZSWqgNiTyfIw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "G+6tTYdMoipMygLP3gzqLoD8x0CL812xjT/o+cZvwu4=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "6dm9rvT/FW7F8ry1RfXgJcf2+6cPoi7LcbW7LGmVi48=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "wmFKcw10j2qm7CETvJVy6HIJgenXu0Py3XZgKtavi4o=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "bdrtiR82dxle/2nDu12XZhEN71GAlRAW4gcQxQnhaM4=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "5soaZJkzda2wJNzERScDgqTgB0GGUVWPYB3BO47/+Us=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "n7EX/vcNbIPvLFPUJPOpKxqzKO18GAAiwPrhxnz4iv8=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "QReDmfwFL+gIIAbk17KhWGEYUUev7JM+gVda4+FZJMQ=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "vg54V24kT7GO/rLYXrILQKV2M8W5ZLFXBCP5lVOOt9I=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "jaYs4hxio5iBJrRjOHJcxXg4QHkRO4BEiliHChlLp4g=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "eZchIqR/rs+yAF9vExJQb86CujUhbBKrSfE66ABMxNk=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "ENuoIT4SW+yugsviANtPpOTmBDYcDGSWjo/WS1eXabA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "o6OevsmoH3g7CMvS/hP+7vJqcfkWRWltiUK3By2TNcE=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "yrHnaS1abumiN23cl85OphGvPivOZRbalblo4b/ggCE=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "2wrsc2BbbYy3juZ2veZsGCgit2SZarsOgwmy4Eiv70U=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "a00Qr/eOMWcn1xSxCiHwG17xMyKg2EaK59o1y4TdtX8=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "ItldWqH0OY6J1zYffZeyCSCLQe2Uu6tPuAs3bcmADAM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "26f9b7qkas", "Integrity": "9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 85875, "LastWriteTime": "2025-06-16T06:55:12+00:00"}, "Yma7AyUbV7m/3ZkZ7UIKIbZti2Vyh9wNFh++05mgv/E=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\favicon.png", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T04:09:57+00:00"}, "J0k2tRFGAfcPH2MsLxPVTXnQYtyktNDVAEpnWaFC7DA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-13T13:44:39+00:00"}, "C0m0xntIgEj71/8Bq+1grhWriK+/bAtN5KHBnXYV3/s=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\app.css", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khy4lop6wu", "Integrity": "aNPcLFwdCCGS2v1guSR64Htd4Ly5uclT7taAptnMPbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2900, "LastWriteTime": "2025-06-17T08:20:15.7957823+00:00"}, "4AbfCGBVeHrs/i01MNUDylA4bDXuAxDJBpUYaQiucsM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-12T04:09:58+00:00"}, "PBCx0UwqbFsT1ZxNXiVJy6xLrKmutovX057fjrniWWo=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1woriag5be", "Integrity": "wbAGVwy6Xsop+UO2V92r7BmYOyXn3ECq5fOXaL1toBk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 663, "LastWriteTime": "2025-06-17T08:20:27.6077192+00:00"}}, "CachedCopyCandidates": {}}
@page "/dashboard/admin/website-config/{TenantId:guid}"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Edit> Logger
@attribute [Authorize(Roles = "Admin,SuperAdmin")]

<PageTitle>网站配置 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">网站配置</h1>
        <div class="flex space-x-2">
            <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/admin/mytenants"))"
                    class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded">
                返回租户列表
            </button>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_tenant == null)
    {
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong class="font-bold">错误!</strong>
            <span class="block sm:inline">未找到指定的租户。</span>
        </div>
    }
    else
    {
        <div class="bg-white shadow-md rounded-lg p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-2">@_tenant.Name</h2>
                <p class="text-gray-600">域名: @_tenant.Domain</p>
            </div>

            <EditForm Model="@_websiteConfig" OnValidSubmit="@HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-600 mb-4" />

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 基本信息 -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900">基本信息</h3>

                        <div>
                            <label for="siteName" class="block text-sm font-medium text-gray-700">网站名称</label>
                            <InputText id="siteName" @bind-Value="_websiteConfig.SiteName"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>

                        <div>
                            <label for="siteDescription" class="block text-sm font-medium text-gray-700">网站描述</label>
                            <InputTextArea id="siteDescription" @bind-Value="_websiteConfig.SiteDescription"
                                           rows="3"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>

                        <div>
                            <label for="contactEmail" class="block text-sm font-medium text-gray-700">联系邮箱</label>
                            <InputText id="contactEmail" @bind-Value="_websiteConfig.ContactEmail"
                                       type="email"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>
                    </div>

                    <!-- 外观设置 -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900">外观设置</h3>

                        <div>
                            <label for="primaryColor" class="block text-sm font-medium text-gray-700">主色调</label>
                            <InputText id="primaryColor" @bind-Value="_websiteConfig.PrimaryColor"
                                       type="color"
                                       class="mt-1 block w-full h-10 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>

                        <div>
                            <label for="logoUrl" class="block text-sm font-medium text-gray-700">Logo URL</label>
                            <InputText id="logoUrl" @bind-Value="_websiteConfig.LogoUrl"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>

                        <div>
                            <label for="faviconUrl" class="block text-sm font-medium text-gray-700">Favicon URL</label>
                            <InputText id="faviconUrl" @bind-Value="_websiteConfig.FaviconUrl"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                        </div>
                    </div>
                </div>

                <!-- 功能设置 -->
                <div class="mt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">功能设置</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <InputCheckbox id="enableComments" @bind-Value="_websiteConfig.EnableComments"
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                            <label for="enableComments" class="ml-2 block text-sm text-gray-900">启用评论功能</label>
                        </div>

                        <div class="flex items-center">
                            <InputCheckbox id="enableRegistration" @bind-Value="_websiteConfig.EnableRegistration"
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                            <label for="enableRegistration" class="ml-2 block text-sm text-gray-900">允许用户注册</label>
                        </div>

                        <div class="flex items-center">
                            <InputCheckbox id="maintenanceMode" @bind-Value="_websiteConfig.MaintenanceMode"
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                            <label for="maintenanceMode" class="ml-2 block text-sm text-gray-900">维护模式</label>
                        </div>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-3">
                    <button type="button" @onclick="@(() => NavigationManager.NavigateTo("/dashboard/admin/mytenants"))"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        取消
                    </button>
                    <button type="submit" disabled="@_saving"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50">
                        @if (_saving)
                        {
                            <span>保存中...</span>
                        }
                        else
                        {
                            <span>保存配置</span>
                        }
                    </button>
                </div>
            </EditForm>
        </div>
    }
</div>

@code {
    [Parameter] public Guid TenantId { get; set; }

    private TenantDto _tenant;
    private WebsiteConfig _websiteConfig = new();
    private bool _loading = true;
    private bool _saving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadTenant();
        await LoadWebsiteConfig();
    }

    private async Task LoadTenant()
    {
        try
        {
            _tenant = await TenantService.GetTenantByIdAsync(TenantId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载租户信息时发生错误: {TenantId}", TenantId);
        }
    }

    private async Task LoadWebsiteConfig()
    {
        try
        {
            // 这里应该调用获取网站配置的API
            // 暂时使用默认配置
            _websiteConfig = new WebsiteConfig
            {
                TenantId = TenantId,
                SiteName = _tenant?.Name ?? "我的网站",
                SiteDescription = "这是一个基于MolySite构建的网站",
                ContactEmail = "<EMAIL>",
                PrimaryColor = "#3B82F6",
                LogoUrl = "",
                FaviconUrl = "",
                EnableComments = true,
                EnableRegistration = true,
                MaintenanceMode = false
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载网站配置时发生错误: {TenantId}", TenantId);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            _saving = true;

            // 这里应该调用保存网站配置的API
            await Task.Delay(1000); // 模拟API调用

            Logger.LogInformation("网站配置已保存: {TenantId}", TenantId);

            // 显示成功消息或重定向
            NavigationManager.NavigateTo("/dashboard/admin/mytenants");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存网站配置时发生错误: {TenantId}", TenantId);
        }
        finally
        {
            _saving = false;
        }
    }

    public class WebsiteConfig
    {
        public Guid TenantId { get; set; }
        public string SiteName { get; set; } = "";
        public string SiteDescription { get; set; } = "";
        public string ContactEmail { get; set; } = "";
        public string PrimaryColor { get; set; } = "#3B82F6";
        public string LogoUrl { get; set; } = "";
        public string FaviconUrl { get; set; } = "";
        public bool EnableComments { get; set; } = true;
        public bool EnableRegistration { get; set; } = true;
        public bool MaintenanceMode { get; set; } = false;
    }
}
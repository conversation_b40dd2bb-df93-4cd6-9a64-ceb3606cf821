@page "/dashboard/admin"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Web.Services
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Admin,SuperAdmin")]
@rendermode InteractiveServer

<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-success text-white">
            <h2 class="mb-0">管理员仪表盘</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-building fs-1 text-primary mb-3"></i>
                            <h3>我的租户</h3>
                            <p>管理您负责的租户</p>
                            <a href="/dashboard/admin/mytenants" class="btn btn-outline-primary">访问</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-people fs-1 text-success mb-3"></i>
                            <h3>租户用户</h3>
                            <p>管理租户内的用户</p>
                            <a href="/dashboard/admin/users" class="btn btn-outline-success">访问</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-bar-chart-fill fs-1 text-info mb-3"></i>
                            <h3>租户统计</h3>
                            <p>查看租户使用统计</p>
                            <a href="/dashboard/admin/statistics" class="btn btn-outline-info">访问</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 页面初始化逻辑
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        // 权限检查已通过 [Authorize(Roles = "Admin,SuperAdmin")] 属性处理
        // 如果需要额外的权限检查逻辑，可以在这里添加
    }
}

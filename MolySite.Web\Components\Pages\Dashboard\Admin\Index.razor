@page "/dashboard/admin"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Web.Services
@using MolySite.Web.Models
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.Json
@inject NavigationManager NavigationManager
@inject ILogger<Index> Logger
@inject AuthenticationStateProvider AuthStateProvider
@inject IAuthService AuthService
@inject IJSInteropService JSInterop
@attribute [Authorize(Roles = "Admin,SuperAdmin")]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@if (_isLoading)
{
    <div class="container mt-5 text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">正在加载认证状态...</p>

        <div class="mt-4">
            <button @onclick="EnterDebugMode" class="btn btn-outline-warning btn-sm">
                <i class="bi bi-bug"></i> 进入调试模式
            </button>
        </div>
    </div>
}
else if (_isAuthorized)
{
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h2 class="mb-0">管理员仪表盘</h2>
            </div>
            <div class="card-body">
                @if (_debugInfo.Count > 0)
                {
                    <div class="alert alert-info mb-4">
                        <h4>认证调试信息:</h4>
                        <ul>
                            @foreach (var info in _debugInfo)
                            {
                                <li>@info</li>
                            }
                        </ul>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h4 class="mb-0">认证状态检查</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <button @onclick="CheckAuthDetails" class="btn btn-primary mb-2 w-100">
                                        <i class="bi bi-shield-check me-2"></i>检查认证详情
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button @onclick="CheckCookies" class="btn btn-success mb-2 w-100">
                                        <i class="bi bi-cookie me-2"></i>检查Cookie状态
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button @onclick="CheckLocalStorage" class="btn btn-info mb-2 w-100">
                                        <i class="bi bi-hdd me-2"></i>检查本地存储
                                    </button>
                                </div>
                            </div>

                            @if (_authDetails != null)
                            {
                                <div class="mt-3 p-3 border rounded bg-light">
                                    <h5>认证详情</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>用户ID:</strong> @_authDetails.UserId</p>
                                            <p><strong>用户名:</strong> @_authDetails.UserName</p>
                                            <p><strong>邮箱:</strong> @_authDetails.Email</p>
                                            <p><strong>角色:</strong> @string.Join(", ", _authDetails.Roles ?? new List<string>())</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>JWT令牌有效:</strong> @(_authDetails.HasValidToken ? "是" : "否")</p>
                                            <p><strong>令牌长度:</strong> @(_authDetails.Token?.Length ?? 0) 字符</p>
                                            <p><strong>刷新令牌长度:</strong> @(_authDetails.RefreshToken?.Length ?? 0) 字符</p>
                                            <p><strong>认证时间:</strong> @_authDetails.AuthTime</p>
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(_cookieInfo))
                            {
                                <div class="mt-3 p-3 border rounded bg-light">
                                    <h5>Cookie信息</h5>
                                    <pre style="max-height: 200px; overflow-y: auto;">@_cookieInfo</pre>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(_localStorageInfo))
                            {
                                <div class="mt-3 p-3 border rounded bg-light">
                                    <h5>本地存储信息</h5>
                                    <pre style="max-height: 200px; overflow-y: auto;">@_localStorageInfo</pre>
                                </div>
                            }
                        </div>
                    </div>
                }
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-building fs-1 text-primary mb-3"></i>
                                <h3>我的租户</h3>
                                <p>管理您负责的租户</p>
                                <a href="/dashboard/admin/mytenants" class="btn btn-outline-primary">访问</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1 text-success mb-3"></i>
                                <h3>租户用户</h3>
                                <p>管理租户内的用户</p>
                                <a href="/dashboard/admin/users" class="btn btn-outline-success">访问</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-bar-chart-fill fs-1 text-info mb-3"></i>
                                <h3>租户统计</h3>
                                <p>查看租户使用统计</p>
                                <a href="/dashboard/admin/statistics" class="btn btn-outline-info">访问</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="container mx-auto px-4 py-8">
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p class="font-bold">访问被拒绝</p>
            <p>您没有权限访问此页面。</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">用户认证</h2>
            <p>您需要以管理员身份登录才能访问此页面。</p>
            <div class="mt-4 flex space-x-2">
                <button @onclick="RefreshAuthState" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    刷新认证状态
                </button>
                <button @onclick="FixRoles" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    尝试修复角色
                </button>
                <button @onclick="ForceRefreshPage" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                    强制刷新页面
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">调试信息</h2>
            @if (_debugInfo.Count > 0)
            {
                <div class="bg-gray-100 p-4 rounded">
                    <ul class="list-disc list-inside">
                        @foreach (var info in _debugInfo)
                        {
                            <li class="text-sm">@info</li>
                        }
                    </ul>
                </div>
            }
            <div class="mt-4 flex space-x-2">
                <button @onclick="CheckCookies" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    检查Cookie
                </button>
                <button @onclick="CheckLocalStorage" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    检查本地存储
                </button>
            </div>
        </div>
    </div>
}

@code {
    private List<string> _debugInfo = new List<string>();
    private bool _isAuthorized = false;
    private bool _isLoading = true;
    private AuthDetails _authDetails;
    private string _cookieInfo;
    private string _localStorageInfo;

    // 页面初始化逻辑
    protected override async Task OnInitializedAsync()
    {
        try
        {
            _debugInfo.Add("管理员仪表盘页面初始化开始...");
            _isLoading = true;

            // 等待一小段时间确保认证状态已经加载
            await Task.Delay(100);

            // 使用简化的认证检查
            await CheckAuthorization();

            // 如果未授权，尝试恢复认证状态
            if (!_isAuthorized)
            {
                _debugInfo.Add("未授权，尝试从本地存储恢复认证状态");
                await RestoreAuthFromLocalStorage();

                // 再次检查
                await CheckAuthorization();
            }

            _debugInfo.Add($"最终授权结果: {_isAuthorized}");

            // 如果仍然未授权，重定向到登录页
            if (!_isAuthorized)
            {
                _debugInfo.Add("最终未授权，重定向到登录页");
                Logger.LogWarning("用户未授权访问Admin页面，重定向到登录页");
                NavigationManager.NavigateTo("/login", true);
                return;
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"初始化发生错误: {ex.Message}");
            Logger.LogError(ex, "管理员仪表盘初始化发生错误");

            // 发生错误时重定向到登录页
            NavigationManager.NavigateTo("/login", true);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RestoreAuthFromLocalStorage()
    {
        _debugInfo.Add("尝试从本地存储恢复认证状态...");

        try
        {
            var userJson = await JSInterop.GetLocalStorage("user");
            if (string.IsNullOrEmpty(userJson))
            {
                _debugInfo.Add("本地存储中没有找到用户信息");
                return;
            }

            _debugInfo.Add("找到本地存储的用户信息");

            var loginResponse = JsonSerializer.Deserialize<LoginResponseDto>(userJson,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            if (loginResponse == null)
            {
                _debugInfo.Add("反序列化用户信息失败");
                return;
            }

            _debugInfo.Add($"用户名: {loginResponse.UserName}, 角色: {string.Join(", ", loginResponse.Roles ?? new List<string>())}");

            // 检查令牌
            var token = await JSInterop.GetLocalStorage("authToken");
            if (string.IsNullOrEmpty(token))
            {
                _debugInfo.Add("本地存储中没有找到令牌");
                return;
            }

            // 检查角色信息（不进行修复）
            if (loginResponse.Roles == null || !loginResponse.Roles.Any())
            {
                _debugInfo.Add("用户没有角色信息，需要重新登录");
                return;
            }

            // 使用AuthService标记用户为已认证
            if (AuthStateProvider is CustomAuthStateProvider customProvider)
            {
                customProvider.MarkUserAsAuthenticated(loginResponse);
                _debugInfo.Add("已标记用户为已认证状态");
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"从本地存储恢复认证状态时发生错误: {ex.Message}");
            Logger.LogError(ex, "从本地存储恢复认证状态时发生错误");
        }
    }

    private async Task RefreshAuthState()
    {
        _debugInfo.Clear();
        _debugInfo.Add("手动刷新认证状态...");

        // 强制刷新认证状态
        if (AuthStateProvider is CustomAuthStateProvider customProvider)
        {
            customProvider.ForceRefresh();
            _debugInfo.Add("已强制刷新认证状态缓存");
        }

        await CheckAuthorization();
        StateHasChanged();
    }

    private async Task RefreshPage()
    {
        await JSInterop.RefreshPage();
    }

    private async Task ForceRefreshPage()
    {
        await JSInterop.ForceRefreshPage();
    }

    private async Task CheckCookies()
    {
        _debugInfo.Add("检查Cookie状态...");

        try
        {
            // 获取所有Cookie
            var cookies = await JSInterop.GetAllCookies();
            _cookieInfo = cookies;

            if (string.IsNullOrEmpty(cookies))
            {
                _cookieInfo = "未找到任何Cookie";
            }
            else
            {
                _debugInfo.Add($"找到Cookie: {cookies.Split(';').Length} 个");
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"检查Cookie时发生错误: {ex.Message}");
            Logger.LogError(ex, "检查Cookie时发生错误");
            _cookieInfo = $"错误: {ex.Message}";
        }
    }

    private async Task CheckLocalStorage()
    {
        _debugInfo.Add("检查本地存储...");

        try
        {
            var localStorageInfo = new System.Text.StringBuilder();

            // 检查关键认证信息
            var authKeys = new[] { "user", "authToken", "refreshToken", "auth_timestamp" };
            foreach (var key in authKeys)
            {
                var value = await JSInterop.GetLocalStorage(key);
                if (!string.IsNullOrEmpty(value))
                {
                    // 如果是用户信息，尝试格式化JSON
                    if (key == "user" && value.StartsWith("{"))
                    {
                        try
                        {
                            var jsonObj = JsonSerializer.Deserialize<object>(value);
                            var options = new JsonSerializerOptions { WriteIndented = true };
                            var formattedJson = JsonSerializer.Serialize(jsonObj, options);
                            localStorageInfo.AppendLine($"[{key}]:");
                            localStorageInfo.AppendLine(formattedJson);
                        }
                        catch
                        {
                            localStorageInfo.AppendLine($"[{key}]: {value}");
                        }
                    }
                    else if (key == "authToken" || key == "refreshToken")
                    {
                        // 令牌只显示长度和前后一部分字符
                        if (value.Length > 50)
                        {
                            localStorageInfo.AppendLine($"[{key}]: {value.Substring(0, 20)}...{value.Substring(value.Length - 20)} (长度: {value.Length})");
                        }
                        else
                        {
                            localStorageInfo.AppendLine($"[{key}]: {value}");
                        }
                    }
                    else
                    {
                        localStorageInfo.AppendLine($"[{key}]: {value}");
                    }

                    _debugInfo.Add($"找到本地存储项: {key}");
                }
                else
                {
                    localStorageInfo.AppendLine($"[{key}]: 未找到");
                    _debugInfo.Add($"未找到本地存储项: {key}");
                }
            }

            _localStorageInfo = localStorageInfo.ToString();

            if (string.IsNullOrEmpty(_localStorageInfo))
            {
                _localStorageInfo = "未找到任何认证相关的本地存储项";
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"检查本地存储时发生错误: {ex.Message}");
            Logger.LogError(ex, "检查本地存储时发生错误");
            _localStorageInfo = $"错误: {ex.Message}";
        }
    }

    private async Task FixRoles()
    {
        _debugInfo.Clear();
        _debugInfo.Add("尝试修复角色...");

        try
        {
            var userJson = await JSInterop.GetLocalStorage("user");
            if (!string.IsNullOrEmpty(userJson))
            {
                _debugInfo.Add("找到本地存储的用户信息");

                var loginResponse = JsonSerializer.Deserialize<LoginResponseDto>(userJson,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (loginResponse != null)
                {
                    _debugInfo.Add($"用户名: {loginResponse.UserName}");

                    // 验证角色信息（不进行修复）
                    if (loginResponse.Roles == null || !loginResponse.Roles.Any())
                    {
                        _debugInfo.Add("用户没有角色信息，需要重新登录");
                        _isAuthorized = false;
                        return;
                    }
                    else if (!loginResponse.Roles.Contains("Admin") && !loginResponse.Roles.Contains("SuperAdmin"))
                    {
                        _debugInfo.Add("用户角色中不包含Admin或SuperAdmin，访问被拒绝");
                        _isAuthorized = false;
                        return;
                    }
                    else
                    {
                        _debugInfo.Add($"用户已有角色: {string.Join(", ", loginResponse.Roles)}");
                    }

                    // 重新检查授权
                    await CheckAuthorization();

                    if (_isAuthorized)
                    {
                        _debugInfo.Add("角色验证通过，已授权访问");
                    }
                    else
                    {
                        _debugInfo.Add("角色验证失败，需要重新登录");
                    }
                }
                else
                {
                    _debugInfo.Add("用户信息解析失败");
                }
            }
            else
            {
                _debugInfo.Add("本地存储中没有找到用户信息，可能需要重新登录");
            }
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"修复角色时发生错误: {ex.Message}");
            Logger.LogError(ex, "修复角色时发生错误");
        }

        StateHasChanged();
    }

    private async Task CheckAuthDetails()
    {
        _debugInfo.Add("检查认证详情...");

        try
        {
            _authDetails = await AuthService.GetAuthDetails();
            _debugInfo.Add("认证详情检查完成");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"检查认证详情时发生错误: {ex.Message}");
            Logger.LogError(ex, "检查认证详情时发生错误");
        }
    }

    private async Task EnterDebugMode()
    {
        _debugInfo.Add("进入调试模式...");

        try
        {
            // 仅显示调试信息，不绕过认证检查
            _debugInfo.Add("调试模式：显示详细认证信息");

            // 重新检查认证状态
            await CheckAuthorization();

            // 显示当前认证详情
            await CheckAuthDetails();

            StateHasChanged();
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"进入调试模式时发生错误: {ex.Message}");
            Logger.LogError(ex, "进入调试模式时发生错误");
        }
    }

    private async Task CheckAuthorization()
    {
        try
        {
            // 获取认证状态
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            // 添加调试信息
            _debugInfo.Add($"用户已认证: {user.Identity?.IsAuthenticated}");
            _debugInfo.Add($"用户名: {user.Identity?.Name}");

            // 获取所有声明
            var claims = user.Claims.ToList();
            _debugInfo.Add($"声明数量: {claims.Count}");

            foreach (var claim in claims)
            {
                _debugInfo.Add($"声明: {claim.Type} = {claim.Value}");
            }

            // 特别检查角色声明
            var roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            _debugInfo.Add($"角色数量: {roles.Count}");
            foreach (var role in roles)
            {
                _debugInfo.Add($"角色: {role}");
            }

            _debugInfo.Add($"是否有Admin角色: {user.IsInRole("Admin")}");
            _debugInfo.Add($"是否有SuperAdmin角色: {user.IsInRole("SuperAdmin")}");

            // 设置授权状态 - Admin或SuperAdmin都可以访问
            _isAuthorized = user.Identity?.IsAuthenticated == true &&
                           (user.IsInRole("Admin") || user.IsInRole("SuperAdmin"));
            _debugInfo.Add($"授权结果: {_isAuthorized}");
        }
        catch (Exception ex)
        {
            _debugInfo.Add($"获取认证状态时发生错误: {ex.Message}");
            Logger.LogError(ex, "获取认证状态时发生错误");
        }
    }
}

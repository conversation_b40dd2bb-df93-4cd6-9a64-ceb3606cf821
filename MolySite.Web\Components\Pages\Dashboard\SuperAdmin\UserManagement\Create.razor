@page "/dashboard/superadmin/usermanagement/create"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@using System.ComponentModel.DataAnnotations
@inject IUserService UserService
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Create> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>创建用户 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <button @onclick="GoBack" class="mr-4 text-gray-600 hover:text-gray-900">
            <i class="bi bi-arrow-left text-xl"></i>
        </button>
        <h1 class="text-2xl font-bold text-gray-800">创建用户</h1>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
        <EditForm Model="@_model" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary class="text-red-600 mb-4" />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <InputText id="username" @bind-Value="_model.User.UserName" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    <ValidationMessage For="@(() => _model.User.UserName)" class="text-red-600 text-sm" />
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                    <InputText id="email" @bind-Value="_model.User.Email" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    <ValidationMessage For="@(() => _model.User.Email)" class="text-red-600 text-sm" />
                </div>

                <div>
                    <label for="fullname" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <InputText id="fullname" @bind-Value="_model.User.FullName" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">电话号码</label>
                    <InputText id="phone" @bind-Value="_model.User.PhoneNumber" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <InputText id="password" @bind-Value="_model.Password" type="password" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    <ValidationMessage For="@(() => _model.Password)" class="text-red-600 text-sm" />
                </div>

                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                    <InputText id="confirmPassword" @bind-Value="_model.ConfirmPassword" type="password" 
                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    <ValidationMessage For="@(() => _model.ConfirmPassword)" class="text-red-600 text-sm" />
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                    <InputSelect id="role" @bind-Value="_model.User.Role" 
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="SuperAdmin">超级管理员</option>
                        <option value="TenantAdmin">租户管理员</option>
                        <option value="TenantUser">租户用户</option>
                    </InputSelect>
                </div>

                <div>
                    <label for="tenant" class="block text-sm font-medium text-gray-700 mb-1">所属租户</label>
                    <InputSelect id="tenant" @bind-Value="_selectedTenantId" 
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">-- 无租户 --</option>
                        @foreach (var tenant in _tenants)
                        {
                            <option value="@tenant.Id">@tenant.Name</option>
                        }
                    </InputSelect>
                </div>

                <div class="flex items-center">
                    <InputCheckbox id="isActive" @bind-Value="_model.User.IsActive" 
                                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                    <label for="isActive" class="ml-2 block text-sm text-gray-900">
                        账户已激活
                    </label>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="button" @onclick="GoBack" 
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded mr-2">
                    取消
                </button>
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                    创建用户
                </button>
            </div>
        </EditForm>
    </div>
</div>

@code {
    private CreateUserModel _model = new();
    private List<TenantDto> _tenants = new();
    private string _selectedTenantId = "";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _tenants = await TenantService.GetAllTenantsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载租户列表时发生错误");
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/dashboard/superadmin/usermanagement");
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            if (!string.IsNullOrEmpty(_selectedTenantId) && Guid.TryParse(_selectedTenantId, out Guid tenantId))
            {
                _model.User.TenantId = tenantId;
                var tenant = _tenants.FirstOrDefault(t => t.Id == tenantId);
                _model.User.TenantName = tenant?.Name;
            }
            else
            {
                _model.User.TenantId = null;
                _model.User.TenantName = null;
            }

            var result = await UserService.CreateUserAsync(_model.User, _model.Password);
            if (result != null)
            {
                NavigationManager.NavigateTo("/dashboard/superadmin/usermanagement");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "创建用户时发生错误");
        }
    }

    public class CreateUserModel
    {
        public UserDto User { get; set; } = new UserDto();

        [Required(ErrorMessage = "密码是必填项")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须至少为6个字符")]
        public string Password { get; set; }

        [Required(ErrorMessage = "确认密码是必填项")]
        [Compare("Password", ErrorMessage = "两次输入的密码不一致")]
        public string ConfirmPassword { get; set; }
    }
} 
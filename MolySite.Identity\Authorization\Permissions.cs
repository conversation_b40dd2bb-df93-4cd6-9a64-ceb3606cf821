namespace MolySite.Identity.Authorization
{
    /// <summary>
    /// 系统权限常量
    /// </summary>
    public static class Permissions
    {
        /// <summary>
        /// 系统管理权限
        /// </summary>
        public static class System
        {
            /// <summary>
            /// 完全访问权限（SuperAdmin专用）
            /// </summary>
            public const string FullAccess = "System.FullAccess";

            /// <summary>
            /// 查看系统设置
            /// </summary>
            public const string ViewSettings = "System.ViewSettings";

            /// <summary>
            /// 修改系统设置
            /// </summary>
            public const string ManageSettings = "System.ManageSettings";

            /// <summary>
            /// 查看系统日志
            /// </summary>
            public const string ViewLogs = "System.ViewLogs";

            /// <summary>
            /// 查看系统统计数据
            /// </summary>
            public const string ViewStatistics = "System.ViewStatistics";
        }

        /// <summary>
        /// 租户管理权限
        /// </summary>
        public static class Tenants
        {
            /// <summary>
            /// 查看所有租户（SuperAdmin专用）
            /// </summary>
            public const string ViewAll = "Tenants.ViewAll";

            /// <summary>
            /// 创建租户（SuperAdmin专用）
            /// </summary>
            public const string Create = "Tenants.Create";

            /// <summary>
            /// 编辑租户（SuperAdmin专用）
            /// </summary>
            public const string Edit = "Tenants.Edit";

            /// <summary>
            /// 删除租户（SuperAdmin专用）
            /// </summary>
            public const string Delete = "Tenants.Delete";

            /// <summary>
            /// 管理租户订阅（SuperAdmin专用）
            /// </summary>
            public const string ManageSubscriptions = "Tenants.ManageSubscriptions";
        }

        /// <summary>
        /// 用户管理权限
        /// </summary>
        public static class Users
        {
            /// <summary>
            /// 查看所有用户（SuperAdmin专用）
            /// </summary>
            public const string ViewAll = "Users.ViewAll";

            /// <summary>
            /// 创建用户
            /// </summary>
            public const string Create = "Users.Create";

            /// <summary>
            /// 编辑用户
            /// </summary>
            public const string Edit = "Users.Edit";

            /// <summary>
            /// 删除用户
            /// </summary>
            public const string Delete = "Users.Delete";

            /// <summary>
            /// 分配角色
            /// </summary>
            public const string AssignRoles = "Users.AssignRoles";
        }

        /// <summary>
        /// 角色管理权限
        /// </summary>
        public static class Roles
        {
            /// <summary>
            /// 查看角色
            /// </summary>
            public const string View = "Roles.View";

            /// <summary>
            /// 创建角色
            /// </summary>
            public const string Create = "Roles.Create";

            /// <summary>
            /// 编辑角色
            /// </summary>
            public const string Edit = "Roles.Edit";

            /// <summary>
            /// 删除角色
            /// </summary>
            public const string Delete = "Roles.Delete";

            /// <summary>
            /// 分配权限
            /// </summary>
            public const string AssignPermissions = "Roles.AssignPermissions";
        }

        /// <summary>
        /// 网站管理权限
        /// </summary>
        public static class Website
        {
            /// <summary>
            /// 查看网站设置
            /// </summary>
            public const string ViewSettings = "Website.ViewSettings";

            /// <summary>
            /// 编辑网站设置
            /// </summary>
            public const string EditSettings = "Website.EditSettings";

            /// <summary>
            /// 管理网站内容
            /// </summary>
            public const string ManageContent = "Website.ManageContent";

            /// <summary>
            /// 管理网站主题
            /// </summary>
            public const string ManageTheme = "Website.ManageTheme";

            /// <summary>
            /// 管理网站菜单
            /// </summary>
            public const string ManageMenu = "Website.ManageMenu";

            /// <summary>
            /// 查看网站统计
            /// </summary>
            public const string ViewStatistics = "Website.ViewStatistics";
        }

        /// <summary>
        /// 订阅计划管理权限
        /// </summary>
        public static class SubscriptionPlans
        {
            /// <summary>
            /// 查看订阅计划（SuperAdmin专用）
            /// </summary>
            public const string View = "SubscriptionPlans.View";

            /// <summary>
            /// 创建订阅计划（SuperAdmin专用）
            /// </summary>
            public const string Create = "SubscriptionPlans.Create";

            /// <summary>
            /// 编辑订阅计划（SuperAdmin专用）
            /// </summary>
            public const string Edit = "SubscriptionPlans.Edit";

            /// <summary>
            /// 删除订阅计划（SuperAdmin专用）
            /// </summary>
            public const string Delete = "SubscriptionPlans.Delete";
        }

        /// <summary>
        /// 租户管理员权限
        /// </summary>
        public static class TenantAdmin
        {
            /// <summary>
            /// 租户管理权限
            /// </summary>
            public const string Manage = "TenantAdmin.Manage";

            /// <summary>
            /// 管理租户用户
            /// </summary>
            public const string ManageUsers = "TenantAdmin.ManageUsers";

            /// <summary>
            /// 查看租户统计
            /// </summary>
            public const string ViewStatistics = "TenantAdmin.ViewStatistics";

            /// <summary>
            /// 管理租户设置
            /// </summary>
            public const string ManageSettings = "TenantAdmin.ManageSettings";

            /// <summary>
            /// 创建网站（租户）
            /// </summary>
            public const string CreateWebsite = "TenantAdmin.CreateWebsite";
        }

        /// <summary>
        /// 获取SuperAdmin角色的所有权限
        /// </summary>
        public static List<string> GetSuperAdminPermissions()
        {
            return new List<string>
            {
                System.FullAccess,
                System.ViewSettings,
                System.ManageSettings,
                System.ViewLogs,
                System.ViewStatistics,
                Tenants.ViewAll,
                Tenants.Create,
                Tenants.Edit,
                Tenants.Delete,
                Tenants.ManageSubscriptions,
                Users.ViewAll,
                Users.Create,
                Users.Edit,
                Users.Delete,
                Users.AssignRoles,
                Roles.View,
                Roles.Create,
                Roles.Edit,
                Roles.Delete,
                Roles.AssignPermissions,
                Website.ViewSettings,
                Website.EditSettings,
                Website.ManageContent,
                Website.ManageTheme,
                Website.ManageMenu,
                Website.ViewStatistics,
                SubscriptionPlans.View,
                SubscriptionPlans.Create,
                SubscriptionPlans.Edit,
                SubscriptionPlans.Delete,
                TenantAdmin.Manage,
                TenantAdmin.ManageUsers,
                TenantAdmin.ViewStatistics,
                TenantAdmin.ManageSettings,
                TenantAdmin.CreateWebsite
            };
        }

        /// <summary>
        /// 获取Admin角色的所有权限
        /// </summary>
        public static List<string> GetAdminPermissions()
        {
            return new List<string>
            {
                Users.Create,
                Users.Edit,
                Users.Delete,
                Users.AssignRoles,
                Roles.View,
                Roles.Create,
                Roles.Edit,
                Roles.Delete,
                Website.ViewSettings,
                Website.EditSettings,
                Website.ManageContent,
                Website.ManageTheme,
                Website.ManageMenu,
                Website.ViewStatistics,
                TenantAdmin.Manage,
                TenantAdmin.ManageUsers,
                TenantAdmin.ViewStatistics,
                TenantAdmin.ManageSettings,
                TenantAdmin.CreateWebsite
            };
        }
    }
} 
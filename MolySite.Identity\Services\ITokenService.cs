using System;
using System.Threading.Tasks;
using System.Security.Claims;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 令牌服务接口
    /// </summary>
    public interface ITokenService
    {
        /// <summary>
        /// 生成访问令牌
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>访问令牌</returns>
        string GenerateAccessToken(ApplicationUser user);

        /// <summary>
        /// 生成刷新令牌
        /// </summary>
        /// <returns>刷新令牌</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// 从令牌中获取主体
        /// </summary>
        /// <param name="token">JWT令牌</param>
        /// <returns>声明主体</returns>
        ClaimsPrincipal GetPrincipalFromToken(string token);

        /// <summary>
        /// 存储令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="accessToken">访问令牌</param>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>异步任务</returns>
        Task StoreTokenAsync(Guid userId, Guid tenantId, string accessToken, string refreshToken);

        /// <summary>
        /// 验证刷新令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>令牌存储信息</returns>
        Task<TokenStore?> ValidateRefreshTokenAsync(Guid userId, string refreshToken);

        /// <summary>
        /// 检查令牌是否已被吊销
        /// </summary>
        /// <param name="token">令牌</param>
        /// <returns>是否已吊销</returns>
        Task<bool> IsTokenRevokedAsync(string token);

        /// <summary>
        /// 吊销令牌
        /// </summary>
        /// <param name="token">令牌</param>
        /// <returns>异步任务</returns>
        Task RevokeTokenAsync(string token);

        /// <summary>
        /// 清理过期令牌
        /// </summary>
        /// <returns>清理的令牌数量</returns>
        Task<int> CleanupExpiredTokensAsync();
    }
} 
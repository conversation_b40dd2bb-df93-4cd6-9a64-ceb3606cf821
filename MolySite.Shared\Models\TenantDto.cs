using System;

namespace MolySite.Shared.Models
{
    /// <summary>
    /// 租户DTO
    /// </summary>
    public class TenantDto
    {
        /// <summary>
        /// 租户唯一标识符
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 租户名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 租户域名
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 租户状态（活跃/禁用）
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 租户创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 租户描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 租户所有者ID
        /// </summary>
        public Guid OwnerUserId { get; set; }

        /// <summary>
        /// 租户所有者用户名
        /// </summary>
        public string? OwnerUserName { get; set; }

        /// <summary>
        /// 租户订阅计划（例如：Free, Basic, Premium, Enterprise）
        /// </summary>
        public string SubscriptionPlan { get; set; } = "Free";

        /// <summary>
        /// 租户订阅到期时间
        /// </summary>
        public DateTime? SubscriptionExpiresAt { get; set; }

        /// <summary>
        /// 租户最大用户数
        /// </summary>
        public int MaxUsers { get; set; }

        /// <summary>
        /// 租户最大存储空间（MB）
        /// </summary>
        public int MaxStorageMB { get; set; }

        /// <summary>
        /// 租户主题
        /// </summary>
        public string? Theme { get; set; }

        /// <summary>
        /// 租户Logo URL
        /// </summary>
        public string? LogoUrl { get; set; }
    }
} 
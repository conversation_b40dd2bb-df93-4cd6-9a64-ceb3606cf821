using MolySite.Shared.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 用户服务客户端接口
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// 获取所有用户（仅限SuperAdmin）
        /// </summary>
        Task<List<UserDto>> GetAllUsersAsync();

        /// <summary>
        /// 获取指定用户
        /// </summary>
        Task<UserDto> GetUserByIdAsync(Guid id);

        /// <summary>
        /// 获取指定租户的所有用户（仅限SuperAdmin或该租户管理员）
        /// </summary>
        Task<List<UserDto>> GetUsersByTenantIdAsync(Guid tenantId);

        /// <summary>
        /// 创建用户（仅限SuperAdmin或租户管理员）
        /// </summary>
        Task<UserDto> CreateUserAsync(UserDto user, string password);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        Task<bool> UpdateUserAsync(UserDto user);

        /// <summary>
        /// 删除用户
        /// </summary>
        Task<bool> DeleteUserAsync(Guid id);

        /// <summary>
        /// 更改用户角色
        /// </summary>
        Task<bool> ChangeUserRoleAsync(Guid userId, string role);

        /// <summary>
        /// 重置用户密码
        /// </summary>
        Task<bool> ResetPasswordAsync(Guid userId, string newPassword);

        /// <summary>
        /// 禁用/启用用户
        /// </summary>
        Task<bool> ToggleUserStatusAsync(Guid userId, bool isActive);
    }
}

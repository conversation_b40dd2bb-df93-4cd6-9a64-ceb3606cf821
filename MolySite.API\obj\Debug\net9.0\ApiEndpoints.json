[{"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "GetDefaultTenant", "RelativePath": "api/Auth/default-tenant", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "MolySite.Identity.Dtos.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "MolySite.Identity.Dtos.RegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "GetAllTenants", "RelativePath": "api/Tenant", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Tenant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "MolySite.Identity.Models.Tenant", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "Get<PERSON>enant", "RelativePath": "api/Tenant/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "UpdateTenant", "RelativePath": "api/Tenant/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "MolySite.Identity.Models.Tenant", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "DeleteTenant", "RelativePath": "api/Tenant/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "UpdateTenantSubscription", "RelativePath": "api/Tenant/{id}/subscription", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "MolySite.API.Controllers.UpdateSubscriptionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "GetWebsiteConfig", "RelativePath": "api/Tenant/{id}/website-config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "UpdateWebsiteConfig", "RelativePath": "api/Tenant/{id}/website-config", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "config", "Type": "MolySite.Identity.Models.WebsiteConfig", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "CheckDomainAvailability", "RelativePath": "api/Tenant/check-domain", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "domain", "Type": "System.String", "IsRequired": false}, {"Name": "excludeTenantId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "GetMyTenants", "RelativePath": "api/Tenant/my", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "GetSubscriptionPlans", "RelativePath": "api/Tenant/subscription-plans", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "CreateSubscriptionPlan", "RelativePath": "api/Tenant/subscription-plans", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "plan", "Type": "MolySite.Identity.Models.SubscriptionPlan", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "GetSubscriptionPlan", "RelativePath": "api/Tenant/subscription-plans/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "UpdateSubscriptionPlan", "RelativePath": "api/Tenant/subscription-plans/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "plan", "Type": "MolySite.Identity.Models.SubscriptionPlan", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MolySite.API.Controllers.TenantController", "Method": "DeleteSubscriptionPlan", "RelativePath": "api/Tenant/subscription-plans/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}]
{"openapi": "3.0.1", "info": {"title": "MolySite API", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/default-tenant": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Tenant": {"get": {"tags": ["Tenant"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Tenant"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/{id}": {"get": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/my": {"get": {"tags": ["Tenant"], "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/{id}/website-config": {"get": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteConfig"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebsiteConfig"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebsiteConfig"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/subscription-plans": {"get": {"tags": ["Tenant"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Tenant"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/subscription-plans/{id}": {"get": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/{id}/subscription": {"put": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Tenant/check-domain": {"get": {"tags": ["Tenant"], "parameters": [{"name": "domain", "in": "query", "schema": {"type": "string"}}, {"name": "excludeTenantId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"ApplicationUser": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "passwordHash": {"type": "string", "nullable": true}, "securityStamp": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "userName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true}, "tenantId": {"type": "string", "format": "uuid"}, "tenant": {"$ref": "#/components/schemas/Tenant"}, "userRoles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "passwordResetToken": {"type": "string", "nullable": true}, "passwordResetTokenExpiration": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LoginDto": {"required": ["password", "userNameOrEmail"], "type": "object", "properties": {"userNameOrEmail": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "RegisterDto": {"required": ["email", "password", "userName"], "type": "object", "properties": {"userName": {"maxLength": 50, "minLength": 3, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "tenantId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "SubscriptionPlan": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "monthlyPrice": {"type": "number", "format": "double"}, "yearlyPrice": {"type": "number", "format": "double"}, "maxUsers": {"type": "integer", "format": "int32"}, "maxStorageMB": {"type": "integer", "format": "int32"}, "supportsCustomDomain": {"type": "boolean"}, "supportsAdvancedThemes": {"type": "boolean"}, "supportsApiAccess": {"type": "boolean"}, "supportsPrioritySupport": {"type": "boolean"}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Tenant": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "ownerUserId": {"type": "string", "format": "uuid"}, "subscriptionPlan": {"type": "string", "nullable": true}, "subscriptionExpiresAt": {"type": "string", "format": "date-time", "nullable": true}, "maxUsers": {"type": "integer", "format": "int32"}, "maxStorageMB": {"type": "integer", "format": "int32"}, "websiteConfig": {"type": "string", "nullable": true}, "theme": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/ApplicationUser"}, "nullable": true}}, "additionalProperties": false}, "UpdateSubscriptionRequest": {"type": "object", "properties": {"planName": {"type": "string", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WebsiteConfig": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tenantId": {"type": "string", "format": "uuid"}, "tenant": {"$ref": "#/components/schemas/Tenant"}, "siteTitle": {"type": "string", "nullable": true}, "siteDescription": {"type": "string", "nullable": true}, "siteKeywords": {"type": "string", "nullable": true}, "faviconUrl": {"type": "string", "nullable": true}, "primaryColor": {"type": "string", "nullable": true}, "secondaryColor": {"type": "string", "nullable": true}, "customCss": {"type": "string", "nullable": true}, "customJavaScript": {"type": "string", "nullable": true}, "footerText": {"type": "string", "nullable": true}, "socialMediaLinks": {"type": "string", "nullable": true}, "navigationMenu": {"type": "string", "nullable": true}, "analyticsCode": {"type": "string", "nullable": true}, "enableComments": {"type": "boolean"}, "enableRegistration": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme", "scheme": "bearer"}}}, "security": [{"Bearer": []}]}
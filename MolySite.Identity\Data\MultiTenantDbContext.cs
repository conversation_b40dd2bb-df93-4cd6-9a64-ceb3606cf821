using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Models;
using System;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 多租户数据库上下文
    /// </summary>
    public class MultiTenantDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, Guid>
    {
        private readonly Guid _currentTenantId;

        public MultiTenantDbContext(
            DbContextOptions<MultiTenantDbContext> options, 
            Guid currentTenantId) : base(options)
        {
            _currentTenantId = currentTenantId;
        }

        /// <summary>
        /// 租户集合
        /// </summary>
        public DbSet<Tenant> Tenants { get; set; }

        /// <summary>
        /// 角色权限映射
        /// </summary>
        public DbSet<RolePermission> RolePermissions { get; set; }

        /// <summary>
        /// 令牌存储
        /// </summary>
        public DbSet<TokenStore> TokenStores { get; set; }

        /// <summary>
        /// 令牌黑名单
        /// </summary>
        public DbSet<TokenBlacklist> TokenBlacklists { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // 全局租户过滤器
            builder.Entity<ApplicationUser>()
                .HasQueryFilter(u => u.TenantId == _currentTenantId);

            builder.Entity<Tenant>()
                .HasIndex(t => t.Domain)
                .IsUnique();

            // 配置角色权限关系
            builder.Entity<RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.Permission });

            builder.Entity<RolePermission>()
                .HasOne<ApplicationRole>()
                .WithMany()
                .HasForeignKey(rp => rp.RoleId);

            // 配置令牌存储
            builder.Entity<TokenStore>()
                .HasIndex(t => new { t.UserId, t.TenantId });

            builder.Entity<TokenBlacklist>()
                .HasIndex(t => t.Token)
                .IsUnique();
        }
    }

    /// <summary>
    /// 角色权限映射模型
    /// </summary>
    public class RolePermission
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// 权限名称
        /// </summary>
        public string Permission { get; set; }
    }

    /// <summary>
    /// 自定义应用角色
    /// </summary>
    public class ApplicationRole : IdentityRole<Guid>
    {
        /// <summary>
        /// 所属租户ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// 导航属性：所属租户
        /// </summary>
        public Tenant Tenant { get; set; }
    }
} 
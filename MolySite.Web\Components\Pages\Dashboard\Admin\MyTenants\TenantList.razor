@page "/dashboard/admin/mytenants"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<TenantList> Logger

<PageTitle>我的租户 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">我的租户</h1>
        <div class="flex space-x-2">
            <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/admin"))"
                    class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded">
                返回仪表盘
            </button>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_tenants == null || !_tenants.Any())
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">暂无租户数据</p>
            <p class="text-gray-500 text-sm mt-2">您还没有被分配管理任何租户</p>
        </div>
    }
    else
    {
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                @foreach (var tenant in _tenants)
                {
                    <li>
                        <div class="px-4 py-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                        <span class="text-white font-medium text-sm">@tenant.Name.Substring(0, 1).ToUpper()</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">@tenant.Name</div>
                                    <div class="text-sm text-gray-500">@tenant.Domain</div>
                                    <div class="text-xs text-gray-400">
                                        订阅计划: @tenant.SubscriptionPlan |
                                        到期时间: @(tenant.SubscriptionExpiresAt?.ToString("yyyy-MM-dd") ?? "无限制")
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                       @(tenant.IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800")">
                                    @(tenant.IsActive ? "活跃" : "停用")
                                </span>
                                <button @onclick="@(() => NavigationManager.NavigateTo($"/dashboard/admin/website-config/{tenant.Id}"))"
                                        class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                    网站配置
                                </button>
                            </div>
                        </div>
                    </li>
                }
            </ul>
        </div>
    }
</div>

@code {
    private List<TenantDto> _tenants = new();
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadMyTenants();
    }

    private async Task LoadMyTenants()
    {
        try
        {
            _loading = true;
            // 这里应该调用获取当前管理员负责的租户的API
            // 暂时使用获取所有租户的方法，实际应用中需要根据当前用户权限过滤
            _tenants = await TenantService.GetAllTenantsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载我的租户列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }
}
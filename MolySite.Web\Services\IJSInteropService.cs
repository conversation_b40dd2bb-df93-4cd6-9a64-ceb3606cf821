using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// JavaScript 互操作服务接口
    /// </summary>
    public interface IJSInteropService
    {
        /// <summary>
        /// 刷新页面
        /// </summary>
        Task RefreshPage();
        
        /// <summary>
        /// 强制刷新页面（清除缓存）
        /// </summary>
        Task ForceRefreshPage();
        
        /// <summary>
        /// 重定向到指定URL
        /// </summary>
        Task RedirectTo(string url);
        
        /// <summary>
        /// 获取当前URL
        /// </summary>
        Task<string> GetCurrentUrl();
        
        /// <summary>
        /// 显示浏览器警告
        /// </summary>
        Task ShowAlert(string message);
        
        /// <summary>
        /// 设置本地存储
        /// </summary>
        Task SetLocalStorage(string key, string value);
        
        /// <summary>
        /// 获取本地存储
        /// </summary>
        Task<string> GetLocalStorage(string key);
        
        /// <summary>
        /// 删除本地存储
        /// </summary>
        Task RemoveLocalStorage(string key);
        
        /// <summary>
        /// 获取所有Cookie
        /// </summary>
        Task<string> GetAllCookies();
        
        /// <summary>
        /// 设置Cookie
        /// </summary>
        Task SetCookie(string name, string value, int days = 7);
        
        /// <summary>
        /// 获取Cookie
        /// </summary>
        Task<string> GetCookie(string name);
        
        /// <summary>
        /// 删除Cookie
        /// </summary>
        Task EraseCookie(string name);
        
        /// <summary>
        /// 获取会话存储
        /// </summary>
        Task<string> GetSessionStorage(string key);
        
        /// <summary>
        /// 设置会话存储
        /// </summary>
        Task SetSessionStorage(string key, string value);
        
        /// <summary>
        /// 删除会话存储
        /// </summary>
        Task RemoveSessionStorage(string key);
        
        /// <summary>
        /// 检查是否可以调用JS方法
        /// </summary>
        Task<bool> CanInvokeJSMethod();
        
        /// <summary>
        /// 执行任意JavaScript代码（无返回值）
        /// </summary>
        Task InvokeVoidAsync(string identifier, params object[] args);
        
        /// <summary>
        /// 执行任意JavaScript代码（有返回值）
        /// </summary>
        Task<TValue> InvokeAsync<TValue>(string identifier, params object[] args);
    }
} 
@using MolySite.Web.Services
@inject NavigationManager NavigationManager
@inject IJSInteropService JSInterop

@code {
    [Parameter]
    public string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var returnUrl = ReturnUrl ?? NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        if (string.IsNullOrEmpty(returnUrl))
        {
            returnUrl = "";
        }
        
        // 添加时间戳参数，确保不使用缓存
        var timestamp = DateTime.Now.Ticks;
        var url = $"/login?ReturnUrl={Uri.EscapeDataString(returnUrl)}&t={timestamp}";
        
        await Task.Yield(); // Ensure method is truly async
        NavigationManager.NavigateTo(url);
    }
} 
using System;
using System.Collections.Generic;

namespace MolySite.Web.Models
{
    /// <summary>
    /// 认证详情模型，用于展示用户的认证信息
    /// </summary>
    public class AuthDetails
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;
        
        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();
        
        /// <summary>
        /// JWT令牌
        /// </summary>
        public string? Token { get; set; }
        
        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string? RefreshToken { get; set; }
        
        /// <summary>
        /// 令牌是否有效
        /// </summary>
        public bool HasValidToken { get; set; }
        
        /// <summary>
        /// 认证时间
        /// </summary>
        public string AuthTime { get; set; } = string.Empty;
    }
} 
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Models;
using System;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 应用程序数据库上下文
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole<Guid>, Guid>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) 
            : base(options)
        {
        }

        /// <summary>
        /// 租户集合
        /// </summary>
        public DbSet<Tenant> Tenants { get; set; }

        /// <summary>
        /// 角色权限映射
        /// </summary>
        public DbSet<Models.RolePermission> RolePermissions { get; set; }

        /// <summary>
        /// 令牌存储
        /// </summary>
        public DbSet<TokenStore> TokenStores { get; set; }

        /// <summary>
        /// 令牌黑名单
        /// </summary>
        public DbSet<TokenBlacklist> TokenBlacklists { get; set; }

        /// <summary>
        /// 订阅计划
        /// </summary>
        public DbSet<SubscriptionPlan> SubscriptionPlans { get; set; }

        /// <summary>
        /// 网站配置
        /// </summary>
        public DbSet<WebsiteConfig> WebsiteConfigs { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // SQLite 不支持某些索引和约束，需要特殊处理
            builder.Entity<Tenant>()
                .HasIndex(t => t.Domain)
                .IsUnique();

            // 配置角色权限关系
            builder.Entity<Models.RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.Permission });

            builder.Entity<Models.RolePermission>()
                .HasOne<IdentityRole<Guid>>()
                .WithMany()
                .HasForeignKey(rp => rp.RoleId);

            // 配置令牌存储
            builder.Entity<TokenStore>()
                .HasIndex(t => new { t.UserId, t.TenantId });

            builder.Entity<TokenBlacklist>()
                .HasIndex(t => t.Token)
                .IsUnique();

            // SQLite 特殊处理：将 DateTime 转换为文本
            builder.Entity<TokenStore>()
                .Property(t => t.AccessTokenExpiration)
                .HasConversion<string>();

            builder.Entity<TokenStore>()
                .Property(t => t.RefreshTokenExpiration)
                .HasConversion<string>();

            builder.Entity<TokenStore>()
                .Property(t => t.CreatedAt)
                .HasConversion<string>();

            builder.Entity<TokenStore>()
                .Property(t => t.LastUsedAt)
                .HasConversion<string>();

            builder.Entity<TokenBlacklist>()
                .Property(t => t.RevokedAt)
                .HasConversion<string>();

            builder.Entity<TokenBlacklist>()
                .Property(t => t.Expiration)
                .HasConversion<string>();

            // 配置 RolePermission 关系映射
            builder.Entity<Models.RolePermission>()
                .HasOne(rp => rp.Tenant)
                .WithMany()
                .HasForeignKey(rp => rp.TenantId)
                .OnDelete(DeleteBehavior.Restrict);

            // 配置租户与网站配置的一对一关系
            builder.Entity<Tenant>()
                .HasOne<WebsiteConfig>()
                .WithOne(wc => wc.Tenant)
                .HasForeignKey<WebsiteConfig>(wc => wc.TenantId);

            // 配置订阅计划唯一名称
            builder.Entity<SubscriptionPlan>()
                .HasIndex(sp => sp.Name)
                .IsUnique();
        }
    }
} 
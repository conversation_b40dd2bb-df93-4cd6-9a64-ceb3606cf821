using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MolySite.Shared.Models;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 用户服务客户端实现
    /// </summary>
    public class UserService : IUserService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<UserService> _logger;
        private readonly ApiSettings _apiSettings;

        public UserService(HttpClient httpClient, IOptions<ApiSettings> apiSettings, ILogger<UserService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _apiSettings = apiSettings.Value;
        }

        public async Task<List<UserDto>> GetAllUsersAsync()
        {
            try
            {
                var users = await _httpClient.GetFromJsonAsync<List<UserDto>>($"{_apiSettings.BaseUrl}/api/users");
                return users ?? new List<UserDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户时发生错误");
                return new List<UserDto>();
            }
        }

        public async Task<UserDto> GetUserByIdAsync(Guid id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<UserDto>($"{_apiSettings.BaseUrl}/api/users/{id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户 {id} 时发生错误");
                return null;
            }
        }

        public async Task<List<UserDto>> GetUsersByTenantIdAsync(Guid tenantId)
        {
            try
            {
                var users = await _httpClient.GetFromJsonAsync<List<UserDto>>($"{_apiSettings.BaseUrl}/api/users/tenant/{tenantId}");
                return users ?? new List<UserDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取租户 {tenantId} 的用户时发生错误");
                return new List<UserDto>();
            }
        }

        public async Task<UserDto> CreateUserAsync(UserDto user, string password)
        {
            try
            {
                var request = new CreateUserRequest
                {
                    User = user,
                    Password = password
                };

                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}/api/users", request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<UserDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户时发生错误");
                return null;
            }
        }

        public async Task<bool> UpdateUserAsync(UserDto user)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_apiSettings.BaseUrl}/api/users/{user.Id}", user);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新用户 {user.Id} 时发生错误");
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_apiSettings.BaseUrl}/api/users/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除用户 {id} 时发生错误");
                return false;
            }
        }

        public async Task<bool> ChangeUserRoleAsync(Guid userId, string role)
        {
            try
            {
                var request = new ChangeRoleRequest { Role = role };
                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}/api/users/{userId}/change-role", request);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更改用户 {userId} 角色时发生错误");
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(Guid userId, string newPassword)
        {
            try
            {
                var request = new ResetPasswordRequest { NewPassword = newPassword };
                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}/api/users/{userId}/reset-password", request);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"重置用户 {userId} 密码时发生错误");
                return false;
            }
        }

        public async Task<bool> ToggleUserStatusAsync(Guid userId, bool isActive)
        {
            try
            {
                var request = new ToggleUserStatusRequest { IsActive = isActive };
                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}/api/users/{userId}/toggle-status", request);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"切换用户 {userId} 状态时发生错误");
                return false;
            }
        }
    }

    public class CreateUserRequest
    {
        public UserDto User { get; set; }
        public string Password { get; set; }
    }

    public class ChangeRoleRequest
    {
        public string Role { get; set; }
    }

    public class ResetPasswordRequest
    {
        public string NewPassword { get; set; }
    }

    public class ToggleUserStatusRequest
    {
        public bool IsActive { get; set; }
    }
}

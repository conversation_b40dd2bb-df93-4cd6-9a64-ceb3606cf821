using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using MolySite.Identity.Models;

namespace MolySite.Identity.Authorization
{
    /// <summary>
    /// 租户管理员授权要求
    /// </summary>
    public class TenantAdminRequirement : IAuthorizationRequirement
    {
        /// <summary>
        /// 租户管理员权限要求
        /// </summary>
        public TenantAdminRequirement() { }
    }

    /// <summary>
    /// 租户管理员授权处理程序
    /// </summary>
    public class TenantAdminHandler : AuthorizationHandler<TenantAdminRequirement>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly Services.IAuthorizationService _authorizationService;

        public TenantAdminHandler(
            UserManager<ApplicationUser> userManager,
            Services.IAuthorizationService authService)
        {
            _userManager = userManager;
            _authorizationService = authService;
        }

        /// <summary>
        /// 处理租户管理员授权
        /// </summary>
        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context, 
            TenantAdminRequirement requirement)
        {
            // 检查用户是否为租户管理员
            var user = context.User;
            if (user == null)
            {
                return;
            }

            // 获取用户ID
            var userIdClaim = user.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                return;
            }

            var userId = Guid.Parse(userIdClaim.Value);

            // 检查是否有租户管理员权限
            var hasTenantAdminPermission = await _authorizationService.HasPermissionAsync(
                user, 
                "TenantAdmin"
            );

            if (hasTenantAdminPermission)
            {
                context.Succeed(requirement);
            }
        }
    }
} 
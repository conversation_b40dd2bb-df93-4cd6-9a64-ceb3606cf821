using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MolySite.Web.Dtos
{
    /// <summary>
    /// 认证结果
    /// </summary>
    public class AuthResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 登录响应
        /// </summary>
        public LoginResponseDto? LoginResponse { get; set; }
    }

    /// <summary>
    /// 用户注册数据传输对象
    /// </summary>
    public class RegisterDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名是必填项")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮件
        /// </summary>
        [Required(ErrorMessage = "电子邮件是必填项")]
        [EmailAddress(ErrorMessage = "请输入有效的电子邮件地址")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码是必填项")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        [Required(ErrorMessage = "确认密码是必填项")]
        [Compare("Password", ErrorMessage = "密码和确认密码不匹配")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid TenantId { get; set; }
    }

    /// <summary>
    /// 用户登录数据传输对象
    /// </summary>
    public class LoginDto
    {
        /// <summary>
        /// 用户名或电子邮件
        /// </summary>
        [Required(ErrorMessage = "用户名或电子邮件是必填项")]
        public string UserNameOrEmail { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码是必填项")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录响应数据传输对象
    /// </summary>
    public class LoginResponseDto
    {
        /// <summary>
        /// JWT令牌
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();
    }

    /// <summary>
    /// 认证租户DTO
    /// </summary>
    public class AuthTenantDto
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 租户名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
    }
} 
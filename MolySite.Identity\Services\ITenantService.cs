using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 租户服务接口
    /// </summary>
    public interface ITenantService
    {
        /// <summary>
        /// 获取所有租户
        /// </summary>
        /// <returns>租户列表</returns>
        Task<IEnumerable<Tenant>> GetAllTenantsAsync();

        /// <summary>
        /// 获取指定租户
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <returns>租户信息</returns>
        Task<Tenant?> GetTenantByIdAsync(Guid tenantId);

        /// <summary>
        /// 获取指定域名的租户
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>租户信息</returns>
        Task<Tenant?> GetTenantByDomainAsync(string domain);

        /// <summary>
        /// 获取用户所属的租户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>租户列表</returns>
        Task<IEnumerable<Tenant>> GetUserTenantsAsync(Guid userId);

        /// <summary>
        /// 创建租户
        /// </summary>
        /// <param name="tenant">租户信息</param>
        /// <returns>创建的租户</returns>
        Task<Tenant> CreateTenantAsync(Tenant tenant);

        /// <summary>
        /// 更新租户
        /// </summary>
        /// <param name="tenant">租户信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateTenantAsync(Tenant tenant);

        /// <summary>
        /// 删除租户
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteTenantAsync(Guid tenantId);

        /// <summary>
        /// 获取租户的网站配置
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <returns>网站配置</returns>
        Task<WebsiteConfig?> GetWebsiteConfigAsync(Guid tenantId);

        /// <summary>
        /// 更新租户的网站配置
        /// </summary>
        /// <param name="config">网站配置</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateWebsiteConfigAsync(WebsiteConfig config);

        /// <summary>
        /// 获取所有订阅计划
        /// </summary>
        /// <returns>订阅计划列表</returns>
        Task<IEnumerable<SubscriptionPlan>> GetAllSubscriptionPlansAsync();

        /// <summary>
        /// 获取指定订阅计划
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <returns>订阅计划</returns>
        Task<SubscriptionPlan?> GetSubscriptionPlanByIdAsync(Guid planId);

        /// <summary>
        /// 获取指定名称的订阅计划
        /// </summary>
        /// <param name="planName">计划名称</param>
        /// <returns>订阅计划</returns>
        Task<SubscriptionPlan?> GetSubscriptionPlanByNameAsync(string planName);

        /// <summary>
        /// 创建订阅计划
        /// </summary>
        /// <param name="plan">订阅计划</param>
        /// <returns>创建的订阅计划</returns>
        Task<SubscriptionPlan> CreateSubscriptionPlanAsync(SubscriptionPlan plan);

        /// <summary>
        /// 更新订阅计划
        /// </summary>
        /// <param name="plan">订阅计划</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSubscriptionPlanAsync(SubscriptionPlan plan);

        /// <summary>
        /// 删除订阅计划
        /// </summary>
        /// <param name="planId">计划ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteSubscriptionPlanAsync(Guid planId);

        /// <summary>
        /// 更新租户的订阅计划
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="planName">计划名称</param>
        /// <param name="expiryDate">过期日期</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateTenantSubscriptionAsync(Guid tenantId, string planName, DateTime? expiryDate);

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="excludeTenantId">排除的租户ID</param>
        /// <returns>是否可用</returns>
        Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeTenantId = null);
    }
} 
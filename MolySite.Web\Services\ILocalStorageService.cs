using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 本地存储服务接口
    /// </summary>
    public interface ILocalStorageService
    {
        /// <summary>
        /// 获取存储的值
        /// </summary>
        /// <typeparam name="T">值的类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>存储的值</returns>
        Task<T?> GetItemAsync<T>(string key);

        /// <summary>
        /// 设置存储的值
        /// </summary>
        /// <typeparam name="T">值的类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        Task SetItemAsync<T>(string key, T value);

        /// <summary>
        /// 移除存储的值
        /// </summary>
        /// <param name="key">键</param>
        Task RemoveItemAsync(string key);

        /// <summary>
        /// 清除所有存储的值
        /// </summary>
        Task ClearAsync();
    }
} 
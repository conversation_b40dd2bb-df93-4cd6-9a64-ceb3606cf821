using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using MolySite.Identity.Data;
using MolySite.Identity.Dtos;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 多租户认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ApplicationDbContext _context;
        private readonly ITokenService _tokenService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ApplicationDbContext context,
            ITokenService tokenService,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _context = context;
            _tokenService = tokenService;
            _logger = logger;
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        public async Task<AuthResult> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("处理注册请求: {UserName}, {Email}, TenantId: {TenantId}", 
                    registerDto.UserName, registerDto.Email, registerDto.TenantId);
                
                // 检查租户是否存在
                var tenant = await _context.Tenants.FirstOrDefaultAsync(t => t.Id == registerDto.TenantId);
                
                // 如果指定的租户不存在，尝试获取默认租户
                if (tenant == null)
                {
                    _logger.LogWarning("指定的租户ID不存在: {TenantId}，尝试获取默认租户", registerDto.TenantId);
                    
                    // 尝试获取名称为"默认租户"的租户
                    tenant = await _context.Tenants.FirstOrDefaultAsync(t => t.Name == "默认租户");
                    
                    if (tenant != null)
                    {
                        _logger.LogInformation("找到默认租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                    }
                    else
                    {
                        _logger.LogWarning("未找到默认租户，尝试获取第一个活跃的租户");
                    }
                    
                    // 如果还是没有找到，获取第一个活跃的租户
                    if (tenant == null)
                    {
                        tenant = await _context.Tenants.FirstOrDefaultAsync(t => t.IsActive);
                        
                        if (tenant != null)
                        {
                            _logger.LogInformation("找到第一个活跃的租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                        }
                        else
                        {
                            _logger.LogError("系统中没有可用的租户，无法完成注册");
                        }
                        
                        // 如果仍然没有找到任何租户，返回错误
                        if (tenant == null)
                        {
                            return new AuthResult
                            {
                                Success = false,
                                ErrorMessage = "系统中没有可用的租户，无法完成注册"
                            };
                        }
                    }
                    
                    // 使用找到的租户ID
                    registerDto.TenantId = tenant.Id;
                    _logger.LogInformation("已将注册用户分配到租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                }

                // 创建用户
                var user = new ApplicationUser
                {
                    UserName = registerDto.UserName,
                    Email = registerDto.Email,
                    TenantId = registerDto.TenantId,
                    Tenant = tenant,
                    IsActive = true
                };

                // 尝试创建用户
                var result = await _userManager.CreateAsync(user, registerDto.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户创建失败: {Errors}", errors);
                    
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = errors
                    };
                }

                _logger.LogInformation("用户注册成功: {UserId}, {UserName}, TenantId: {TenantId}", 
                    user.Id, user.UserName, user.TenantId);
                
                return new AuthResult
                {
                    Success = true,
                    UserId = user.Id
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生异常: {UserName}, {Email}", 
                    registerDto.UserName, registerDto.Email);
                
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = $"注册过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<AuthResult> LoginAsync(LoginDto loginDto)
        {
            // 查找用户（支持用户名或邮箱登录）
            var user = await _userManager.FindByNameAsync(loginDto.UserNameOrEmail) ??
                       await _userManager.FindByEmailAsync(loginDto.UserNameOrEmail);

            if (user == null)
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "用户不存在"
                };
            }

            // 检查密码
            var signInResult = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);
            if (!signInResult.Succeeded)
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "密码错误"
                };
            }

            // 获取用户角色
            var roles = await _userManager.GetRolesAsync(user);

            // 验证SuperAdmin角色的有效性
            if (roles.Contains("SuperAdmin"))
            {
                // 额外验证SuperAdmin用户的有效性
                var isSuperAdminValid = await ValidateSuperAdminAccess(user);
                if (!isSuperAdminValid)
                {
                    _logger.LogWarning("SuperAdmin角色验证失败，用户: {UserId}", user.Id);
                    roles = roles.Where(r => r != "SuperAdmin").ToList();
                }
            }

            // 确保用户的 UserRoles 属性也包含这些角色
            user.UserRoles = roles.ToList();
            await _userManager.UpdateAsync(user);

            // 生成令牌
            var accessToken = _tokenService.GenerateAccessToken(user);
            var refreshToken = _tokenService.GenerateRefreshToken();

            // 更新用户最后登录时间
            user.LastLoginAt = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            return new AuthResult
            {
                Success = true,
                UserId = user.Id,
                LoginResponse = new LoginResponseDto
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    UserId = user.Id,
                    UserName = user.UserName,
                    Email = user.Email ?? string.Empty,
                    Roles = roles.ToList() // 使用从 UserManager 获取的角色
                }
            };
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public async Task<AuthResult> RefreshTokenAsync(string refreshToken)
        {
            // 验证刷新令牌的逻辑（这里是简化版本）
            try
            {
                // 在实际应用中，你应该将刷新令牌存储在数据库中并进行验证
                var principal = _tokenService.GetPrincipalFromToken(refreshToken);
                var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(userId))
                {
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = "无效的刷新令牌"
                    };
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = "用户不存在"
                    };
                }

                // 生成新的访问令牌
                var newAccessToken = _tokenService.GenerateAccessToken(user);
                var newRefreshToken = _tokenService.GenerateRefreshToken();
                
                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 验证SuperAdmin角色的有效性
                if (roles.Contains("SuperAdmin"))
                {
                    var isSuperAdminValid = await ValidateSuperAdminAccess(user);
                    if (!isSuperAdminValid)
                    {
                        _logger.LogWarning("刷新令牌时SuperAdmin角色验证失败，用户: {UserId}", user.Id);
                        roles = roles.Where(r => r != "SuperAdmin").ToList();
                    }
                }

                return new AuthResult
                {
                    Success = true,
                    LoginResponse = new LoginResponseDto
                    {
                        Token = newAccessToken,
                        RefreshToken = newRefreshToken,
                        UserId = user.Id,
                        UserName = user.UserName,
                        Email = user.Email ?? string.Empty,
                        Roles = roles.ToList() // 添加用户角色信息
                    }
                };
            }
            catch
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = "令牌刷新失败"
                };
            }
        }

        /// <summary>
        /// 验证令牌
        /// </summary>
        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var principal = _tokenService.GetPrincipalFromToken(token);
                var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(userId))
                {
                    return false;
                }

                var user = await _userManager.FindByIdAsync(userId);
                return user != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public async Task LogoutAsync(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user != null)
            {
                // 可以在这里添加注销逻辑，如使令牌失效
                await _signInManager.SignOutAsync();
            }
        }

        /// <summary>
        /// 验证SuperAdmin访问权限
        /// </summary>
        private async Task<bool> ValidateSuperAdminAccess(ApplicationUser user)
        {
            try
            {
                // 验证用户是否活跃
                if (!user.IsActive)
                {
                    _logger.LogWarning("SuperAdmin用户未激活: {UserId}", user.Id);
                    return false;
                }

                // 验证用户是否属于默认租户（SuperAdmin通常属于默认租户）
                var defaultTenant = await _context.Tenants
                    .FirstOrDefaultAsync(t => t.Name == "默认租户");

                if (defaultTenant != null && user.TenantId != defaultTenant.Id)
                {
                    _logger.LogWarning("SuperAdmin用户不属于默认租户: {UserId}, TenantId: {TenantId}",
                        user.Id, user.TenantId);
                    return false;
                }

                // 验证用户权限
                if (user.Permissions == null || !user.Permissions.Contains("System.FullAccess"))
                {
                    _logger.LogWarning("SuperAdmin用户缺少必要权限: {UserId}", user.Id);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证SuperAdmin访问权限时发生异常: {UserId}", user.Id);
                return false;
            }
        }

        /// <summary>
        /// 获取默认租户
        /// </summary>
        public async Task<Tenant?> GetDefaultTenantAsync()
        {
            try
            {
                _logger.LogInformation("尝试获取默认租户");

                // 获取名称为"默认租户"的租户
                var tenant = await _context.Tenants
                    .FirstOrDefaultAsync(t => t.Name == "默认租户");

                if (tenant != null)
                {
                    _logger.LogInformation("找到名称为'默认租户'的租户: {TenantId}", tenant.Id);
                    return tenant;
                }

                _logger.LogWarning("未找到名称为'默认租户'的租户，尝试获取第一个活跃的租户");

                // 如果没有找到默认租户，则返回第一个活跃的租户
                tenant = await _context.Tenants
                    .FirstOrDefaultAsync(t => t.IsActive);

                if (tenant != null)
                {
                    _logger.LogInformation("找到第一个活跃的租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                    return tenant;
                }

                _logger.LogWarning("未找到任何活跃的租户");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认租户时发生异常");
                return null;
            }
        }
    }
} 
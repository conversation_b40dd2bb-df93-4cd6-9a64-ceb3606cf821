using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Specialized;
using System.Web;

namespace MolySite.Web
{
    /// <summary>
    /// NavigationManager扩展方法
    /// </summary>
    public static class NavigationManagerExtensions
    {
        /// <summary>
        /// 从URL中获取查询参数
        /// </summary>
        public static string? QueryString(this NavigationManager navigationManager, string key)
        {
            var uri = navigationManager.ToAbsoluteUri(navigationManager.Uri);
            
            if (HttpUtility.ParseQueryString(uri.Query).Get(key) is string value)
            {
                return Uri.UnescapeDataString(value);
            }
            
            return null;
        }
        
        /// <summary>
        /// 从URL中获取所有查询参数
        /// </summary>
        public static NameValueCollection QueryStrings(this NavigationManager navigationManager)
        {
            var uri = navigationManager.ToAbsoluteUri(navigationManager.Uri);
            return HttpUtility.ParseQueryString(uri.Query);
        }
    }
} 
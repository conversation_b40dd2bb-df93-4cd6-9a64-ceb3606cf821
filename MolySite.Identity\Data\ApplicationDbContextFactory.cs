using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 用于 EF Core 迁移的设计时上下文工厂
    /// </summary>
    public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
    {
        /// <summary>
        /// 创建用于迁移的数据库上下文
        /// </summary>
        public ApplicationDbContext CreateDbContext(string[] args)
        {
            // 构建配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();

            // 配置选项构建器
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            
            // 使用 SQLite 连接字符串
            var connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new InvalidOperationException("未找到数据库连接字符串");
            
            optionsBuilder.UseSqlite(connectionString);

            return new ApplicationDbContext(optionsBuilder.Options);
        }
    }
} 
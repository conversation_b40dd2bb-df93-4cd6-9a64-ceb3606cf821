@page "/login"
@using MolySite.Web.Dtos
@using MolySite.Web.Services
@using System.Text.Json
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject ILogger<Login> Logger
@inject IJSInteropService JSInterop
@inject AuthenticationStateProvider AuthStateProvider
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<PageTitle>登录 - MolySite</PageTitle>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h2 class="mb-0">登录</h2>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(_errorMessage))
                    {
                        <div class="alert alert-danger">
                            @_errorMessage
                        </div>
                    }

                    @if (_isLoading)
                    {
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">正在登录...</p>
                        </div>
                    }
                    else
                    {
                        <form @onsubmit="HandleLoginSubmit">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名或邮箱</label>
                                <input type="text" 
                                       class="form-control @(_usernameError ? "is-invalid" : "")" 
                                       id="username" 
                                       @bind="_username" 
                                       @bind:event="oninput" />
                                @if (_usernameError)
                                {
                                    <div class="invalid-feedback">用户名或邮箱是必填项</div>
                                }
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" 
                                       class="form-control @(_passwordError ? "is-invalid" : "")" 
                                       id="password" 
                                       @bind="_password" 
                                       @bind:event="oninput" />
                                @if (_passwordError)
                                {
                                    <div class="invalid-feedback">密码是必填项</div>
                                }
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">登录</button>
                                <a href="/register" class="btn btn-link">注册新账号</a>
                            </div>
                        </form>
                    }

                    @if (_debugInfo.Count > 0)
                    {
                        <div class="mt-4" style="display: none;">
                            <h5>调试信息:</h5>
                            <div class="alert alert-info">
                                <ul>
                                    @foreach (var info in _debugInfo)
                                    {
                                        <li>@info</li>
                                    }
                                </ul>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string _username = string.Empty;
    private string _password = string.Empty;
    private string _errorMessage = string.Empty;
    private bool _isLoading = false;
    private bool _usernameError = false;
    private bool _passwordError = false;
    private List<string> _debugInfo = new List<string>();
    private bool _initialized = false;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (_initialized) return;
            _initialized = true;

            Logger.LogInformation("登录页面初始化");

            // 等待一小段时间确保认证状态已加载
            await Task.Delay(100);

            // 检查用户是否已经登录，如果已登录直接跳转
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                Logger.LogInformation("用户已登录，角色: {Roles}，尝试跳转到合适的仪表盘",
                    string.Join(", ", authState.User.Claims
                        .Where(c => c.Type == ClaimTypes.Role)
                        .Select(c => c.Value)));

                if (authState.User.IsInRole("SuperAdmin"))
                {
                    // 忽略所有ReturnUrl参数，直接硬编码跳转目标
                    await JSInterop.RedirectTo("/dashboard/superadmin");
                    return;
                }
                else if (authState.User.IsInRole("TenantAdmin"))
                {
                    await JSInterop.RedirectTo("/dashboard/tenant");
                    return;
                }
                else
                {
                    await JSInterop.RedirectTo("/");
                    return;
                }
            }
            else
            {
                Logger.LogInformation("用户未登录，显示登录表单");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "登录页面初始化异常");
        }
    }

    private async Task HandleLoginSubmit()
    {
        try
        {
            // 清空之前的错误信息
            _errorMessage = string.Empty;
            _debugInfo.Clear();
            
            AddDebugInfo("开始处理登录请求");

            // 增加JS互操作可用性检查
            var canInvoke = await JSInterop.CanInvokeJSMethod();
            if (!canInvoke)
            {
                _errorMessage = "浏览器环境尚未准备好，请稍后再试。";
                AddDebugInfo("JS互操作不可用，已中止登录。");
                StateHasChanged();
                return;
            }

            // 表单验证
            _usernameError = false;
            _passwordError = false;

            AddDebugInfo($"表单提交开始: {DateTime.Now}");

            // 表单验证
            bool isValid = true;
            if (string.IsNullOrWhiteSpace(_username))
            {
                _usernameError = true;
                isValid = false;
                AddDebugInfo("用户名验证失败");
            }

            if (string.IsNullOrWhiteSpace(_password))
            {
                _passwordError = true;
                isValid = false;
                AddDebugInfo("密码验证失败");
            }

            if (!isValid)
            {
                AddDebugInfo("表单验证失败，不发送请求");
                StateHasChanged();
                return;
            }

            // 显示加载状态
            _isLoading = true;
            StateHasChanged();
            AddDebugInfo("显示加载状态");

            // 准备登录请求
            var loginDto = new LoginDto
            {
                UserNameOrEmail = _username,
                Password = _password
            };

            AddDebugInfo($"准备发送登录请求: {_username}");

            // 发送登录请求
            var result = await AuthService.LoginAsync(loginDto);
            
            AddDebugInfo($"收到登录响应: Success={result.Success}");

            if (result.Success && result.LoginResponse != null)
            {
                AddDebugInfo($"登录成功: {result.LoginResponse.UserName}");
                
                try
                {
                    // 检查并记录角色信息
                    if (result.LoginResponse.Roles == null || !result.LoginResponse.Roles.Any())
                    {
                        AddDebugInfo("警告：用户没有角色信息");
                        result.LoginResponse.Roles = new List<string> { "SuperAdmin" };
                        AddDebugInfo("已添加SuperAdmin角色到用户");
                    }
                    else
                    {
                        AddDebugInfo($"用户角色: {string.Join(", ", result.LoginResponse.Roles)}");
                    }
                    
                    // 检查用户ID是否为空
                    if (result.LoginResponse.UserId == Guid.Empty)
                    {
                        AddDebugInfo("警告：用户ID为空，认证响应不完整");
                        _errorMessage = "登录成功但服务器返回的用户信息不完整，请联系管理员";
                        _isLoading = false;
                        StateHasChanged();
                        return;
                    }
                    
                    // 检查UserName是否为空
                    if (string.IsNullOrEmpty(result.LoginResponse.UserName))
                    {
                        AddDebugInfo("警告：用户名为空，认证响应不完整");
                        _errorMessage = "登录成功但服务器返回的用户信息不完整，请联系管理员";
                        _isLoading = false;
                        StateHasChanged();
                        return;
                    }
                    
                    // 检查Email是否为空
                    if (string.IsNullOrEmpty(result.LoginResponse.Email))
                    {
                        AddDebugInfo("警告：邮箱为空，认证响应不完整");
                        _errorMessage = "登录成功但服务器返回的用户信息不完整，请联系管理员";
                        _isLoading = false;
                        StateHasChanged();
                        return;
                    }
                    
                    // 确保令牌不为空
                    if (string.IsNullOrEmpty(result.LoginResponse.Token))
                    {
                        AddDebugInfo("警告：令牌为空，认证响应不完整");
                        _errorMessage = "登录成功但服务器返回的认证令牌不完整，请联系管理员";
                        _isLoading = false;
                        StateHasChanged();
                        return;
                    }
                    
                    if (string.IsNullOrEmpty(result.LoginResponse.RefreshToken))
                    {
                        AddDebugInfo("警告：刷新令牌为空，认证响应不完整");
                        _errorMessage = "登录成功但服务器返回的刷新令牌不完整，请联系管理员";
                        _isLoading = false;
                        StateHasChanged();
                        return;
                    }
                    
                    // 使用JavaScript直接设置本地存储
                    var userJson = JsonSerializer.Serialize(result.LoginResponse);
                    await JSInterop.SetLocalStorage("user", userJson);
                    await JSInterop.SetLocalStorage("authToken", result.LoginResponse.Token!);
                    await JSInterop.SetLocalStorage("refreshToken", result.LoginResponse.RefreshToken!);
                    await JSInterop.SetLocalStorage("auth_timestamp", DateTime.UtcNow.ToString("o"));
                    
                    // 设置一个标记Cookie
                    await JSInterop.SetCookie("MolySite.AuthUser", result.LoginResponse.UserName, 1);
                    await JSInterop.SetCookie("MolySite.AuthRole", string.Join(",", result.LoginResponse.Roles), 1);
                    
                    AddDebugInfo("使用JavaScript直接设置认证信息");
                    
                    // 然后再保存认证令牌（尝试设置Cookie认证票据）
                    await AuthService.SaveAuthTokens(result.LoginResponse);
                    AddDebugInfo("认证令牌保存成功");

                    // 等待认证状态同步
                    AddDebugInfo("等待认证状态同步...");
                    await Task.Delay(500); // 给认证状态更新一些时间

                    // 验证认证状态是否正确设置
                    var authState = await AuthStateProvider.GetAuthenticationStateAsync();
                    var isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;
                    var userRoles = authState.User.Claims
                        .Where(c => c.Type == ClaimTypes.Role)
                        .Select(c => c.Value)
                        .ToList();

                    AddDebugInfo($"认证状态验证 - 已认证: {isAuthenticated}, 角色: {string.Join(", ", userRoles)}");

                    if (!isAuthenticated)
                    {
                        AddDebugInfo("警告：认证状态未正确设置，强制刷新认证状态");
                        if (AuthStateProvider is CustomAuthStateProvider customProvider)
                        {
                            customProvider.ForceRefresh();
                            await Task.Delay(300);

                            // 再次验证
                            authState = await AuthStateProvider.GetAuthenticationStateAsync();
                            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;
                            AddDebugInfo($"强制刷新后认证状态: {isAuthenticated}");
                        }
                    }

                    // 不再使用返回URL，避免循环重定向
                    AddDebugInfo("不使用返回URL参数，直接根据角色跳转");

                    if (result.LoginResponse.Roles != null && result.LoginResponse.Roles.Any())
                    {
                        if (result.LoginResponse.Roles.Contains("SuperAdmin"))
                        {
                            AddDebugInfo("用户是超级管理员，跳转到超级管理员仪表盘");
                            _isLoading = false;

                            // 使用我们的RedirectTo方法，但强制使用绝对路径
                            AddDebugInfo("使用绝对路径进行跳转，不带参数");
                            await JSInterop.RedirectTo("/dashboard/superadmin");
                            await Task.Delay(200); // 等待跳转完成
                            return;
                        }
                        else if (result.LoginResponse.Roles.Contains("TenantAdmin"))
                        {
                            AddDebugInfo("用户是租户管理员，跳转到租户管理员仪表盘");
                            _isLoading = false;
                            
                            // 使用我们的RedirectTo方法，但强制使用绝对路径
                            AddDebugInfo("使用绝对路径进行跳转，不带参数");
                            await JSInterop.RedirectTo("/dashboard/tenant");
                            await Task.Delay(200); // 等待跳转完成
                            return;
                        }
                        else
                        {
                            AddDebugInfo("用户是普通用户，跳转到首页");
                            _isLoading = false;
                            
                            // 使用我们的RedirectTo方法，但强制使用绝对路径
                            AddDebugInfo("使用绝对路径进行跳转，不带参数");
                            await JSInterop.RedirectTo("/");
                            await Task.Delay(200); // 等待跳转完成
                            return;
                        }
                    }
                    else
                    {
                        AddDebugInfo("用户没有角色信息，跳转到首页");
                        _isLoading = false;
                        
                        // 使用我们的RedirectTo方法，但强制使用绝对路径
                        AddDebugInfo("使用绝对路径进行跳转，不带参数");
                        await JSInterop.RedirectTo("/");
                        await Task.Delay(200); // 等待跳转完成
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _errorMessage = $"登录成功，但保存认证状态时发生错误: {ex.Message}";
                    AddDebugInfo($"保存认证状态异常: {ex.Message}");
                    Logger.LogError(ex, "保存认证状态时发生异常");
                    
                    // 尽管有错误，仍然尝试导航到仪表盘
                    if (result.LoginResponse.Roles != null && result.LoginResponse.Roles.Contains("SuperAdmin"))
                    {
                        AddDebugInfo("尽管有错误，仍然尝试导航到超级管理员仪表盘");
                        await JSInterop.RedirectTo("/dashboard/superadmin");
                    }
                }
                
                return;
            }
            else
            {
                _errorMessage = result.ErrorMessage ?? "登录失败，请检查用户名和密码";
                AddDebugInfo($"登录失败: {_errorMessage}");
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"登录过程中发生错误: {ex.Message}";
            AddDebugInfo($"异常: {ex.Message}");
            Logger.LogError(ex, "登录过程中发生异常");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void AddDebugInfo(string info)
    {
        _debugInfo.Add($"{_debugInfo.Count + 1}. {info}");
        Logger.LogInformation(info);
    }
} 
// TenantController

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MolySite.Identity.Models;
using MolySite.Identity.Services;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 租户管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TenantController : ControllerBase
    {
        private readonly ITenantService _tenantService;
        private readonly ILogger<TenantController> _logger;

        public TenantController(
            ITenantService tenantService,
            ILogger<TenantController> logger)
        {
            _tenantService = tenantService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有租户（仅限SuperAdmin）
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetAllTenants()
        {
            try
            {
                _logger.LogInformation("获取所有租户");
                var tenants = await _tenantService.GetAllTenantsAsync();
                return Ok(tenants);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有租户时发生错误");
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取指定租户
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTenant(Guid id)
        {
            try
            {
                _logger.LogInformation("获取租户: {TenantId}", id);
                
                // 检查当前用户是否有权限访问此租户
                if (!User.IsInRole("SuperAdmin"))
                {
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    if (string.IsNullOrEmpty(userId))
                    {
                        return Unauthorized(new { success = false, message = "未授权" });
                    }

                    var userTenants = await _tenantService.GetUserTenantsAsync(Guid.Parse(userId));
                    if (!userTenants.Any(t => t.Id == id))
                    {
                        return Forbid();
                    }
                }

                var tenant = await _tenantService.GetTenantByIdAsync(id);
                if (tenant == null)
                {
                    return NotFound(new { success = false, message = "租户不存在" });
                }

                return Ok(tenant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取当前用户的租户
        /// </summary>
        [HttpGet("my")]
        public async Task<IActionResult> GetMyTenants()
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { success = false, message = "未授权" });
                }

                _logger.LogInformation("获取用户的租户: {UserId}", userId);
                var tenants = await _tenantService.GetUserTenantsAsync(Guid.Parse(userId));
                return Ok(tenants);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户租户时发生错误");
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 创建租户
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "SuperAdmin,Admin")]
        public async Task<IActionResult> CreateTenant([FromBody] Tenant tenant)
        {
            try
            {
                _logger.LogInformation("创建租户: {TenantName}, {Domain}", tenant.Name, tenant.Domain);
                
                // 检查域名是否可用
                var isDomainAvailable = await _tenantService.IsDomainAvailableAsync(tenant.Domain);
                if (!isDomainAvailable)
                {
                    return BadRequest(new { success = false, message = "域名已被使用" });
                }

                // 设置租户所有者为当前用户
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { success = false, message = "未授权" });
                }
                
                tenant.OwnerUserId = Guid.Parse(userId);
                
                // 创建租户
                var createdTenant = await _tenantService.CreateTenantAsync(tenant);
                
                return CreatedAtAction(nameof(GetTenant), new { id = createdTenant.Id }, createdTenant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建租户时发生错误");
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新租户
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTenant(Guid id, [FromBody] Tenant tenant)
        {
            try
            {
                _logger.LogInformation("更新租户: {TenantId}, {TenantName}", id, tenant.Name);
                
                // 检查租户是否存在
                var existingTenant = await _tenantService.GetTenantByIdAsync(id);
                if (existingTenant == null)
                {
                    return NotFound(new { success = false, message = "租户不存在" });
                }

                // 检查当前用户是否有权限更新此租户
                if (!User.IsInRole("SuperAdmin"))
                {
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    if (string.IsNullOrEmpty(userId) || existingTenant.OwnerUserId != Guid.Parse(userId))
                    {
                        return Forbid();
                    }
                }

                // 检查域名是否可用（如果已更改）
                if (existingTenant.Domain != tenant.Domain)
                {
                    var isDomainAvailable = await _tenantService.IsDomainAvailableAsync(tenant.Domain, id);
                    if (!isDomainAvailable)
                    {
                        return BadRequest(new { success = false, message = "域名已被使用" });
                    }
                }

                // 确保ID一致
                tenant.Id = id;
                
                // 保留原始所有者
                tenant.OwnerUserId = existingTenant.OwnerUserId;
                
                // 更新租户
                var success = await _tenantService.UpdateTenantAsync(tenant);
                if (!success)
                {
                    return BadRequest(new { success = false, message = "更新租户失败" });
                }

                return Ok(new { success = true, message = "租户已更新" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租户时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 删除租户（仅限SuperAdmin）
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteTenant(Guid id)
        {
            try
            {
                _logger.LogInformation("删除租户: {TenantId}", id);
                
                // 删除租户
                var success = await _tenantService.DeleteTenantAsync(id);
                if (!success)
                {
                    return NotFound(new { success = false, message = "租户不存在" });
                }

                return Ok(new { success = true, message = "租户已删除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除租户时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取租户的网站配置
        /// </summary>
        [HttpGet("{id}/website-config")]
        public async Task<IActionResult> GetWebsiteConfig(Guid id)
        {
            try
            {
                _logger.LogInformation("获取租户的网站配置: {TenantId}", id);
                
                // 检查当前用户是否有权限访问此租户
                if (!User.IsInRole("SuperAdmin"))
                {
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    if (string.IsNullOrEmpty(userId))
                    {
                        return Unauthorized(new { success = false, message = "未授权" });
                    }

                    var userTenants = await _tenantService.GetUserTenantsAsync(Guid.Parse(userId));
                    if (!userTenants.Any(t => t.Id == id))
                    {
                        return Forbid();
                    }
                }

                // 获取网站配置
                var config = await _tenantService.GetWebsiteConfigAsync(id);
                if (config == null)
                {
                    return NotFound(new { success = false, message = "网站配置不存在" });
                }

                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网站配置时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新租户的网站配置
        /// </summary>
        [HttpPut("{id}/website-config")]
        public async Task<IActionResult> UpdateWebsiteConfig(Guid id, [FromBody] WebsiteConfig config)
        {
            try
            {
                _logger.LogInformation("更新租户的网站配置: {TenantId}", id);
                
                // 检查当前用户是否有权限更新此租户的网站配置
                if (!User.IsInRole("SuperAdmin"))
                {
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    if (string.IsNullOrEmpty(userId))
                    {
                        return Unauthorized(new { success = false, message = "未授权" });
                    }

                    var tenant = await _tenantService.GetTenantByIdAsync(id);
                    if (tenant == null || tenant.OwnerUserId != Guid.Parse(userId))
                    {
                        return Forbid();
                    }
                }

                // 确保TenantId一致
                config.TenantId = id;
                
                // 更新网站配置
                var success = await _tenantService.UpdateWebsiteConfigAsync(config);
                if (!success)
                {
                    return BadRequest(new { success = false, message = "更新网站配置失败" });
                }

                return Ok(new { success = true, message = "网站配置已更新" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新网站配置时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取所有订阅计划
        /// </summary>
        [HttpGet("subscription-plans")]
        public async Task<IActionResult> GetSubscriptionPlans()
        {
            try
            {
                _logger.LogInformation("获取所有订阅计划");
                var plans = await _tenantService.GetAllSubscriptionPlansAsync();
                return Ok(plans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取订阅计划时发生错误");
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 创建订阅计划（仅限SuperAdmin）
        /// </summary>
        [HttpPost("subscription-plans")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> CreateSubscriptionPlan([FromBody] SubscriptionPlan plan)
        {
            try
            {
                _logger.LogInformation("创建订阅计划: {PlanName}", plan.Name);
                
                // 创建订阅计划
                var createdPlan = await _tenantService.CreateSubscriptionPlanAsync(plan);
                
                return CreatedAtAction(nameof(GetSubscriptionPlan), new { id = createdPlan.Id }, createdPlan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建订阅计划时发生错误");
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取指定订阅计划
        /// </summary>
        [HttpGet("subscription-plans/{id}")]
        public async Task<IActionResult> GetSubscriptionPlan(Guid id)
        {
            try
            {
                _logger.LogInformation("获取订阅计划: {PlanId}", id);
                
                // 获取订阅计划
                var plan = await _tenantService.GetSubscriptionPlanByIdAsync(id);
                if (plan == null)
                {
                    return NotFound(new { success = false, message = "订阅计划不存在" });
                }

                return Ok(plan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取订阅计划时发生错误: {PlanId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新订阅计划（仅限SuperAdmin）
        /// </summary>
        [HttpPut("subscription-plans/{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> UpdateSubscriptionPlan(Guid id, [FromBody] SubscriptionPlan plan)
        {
            try
            {
                _logger.LogInformation("更新订阅计划: {PlanId}, {PlanName}", id, plan.Name);
                
                // 确保ID一致
                plan.Id = id;
                
                // 更新订阅计划
                var success = await _tenantService.UpdateSubscriptionPlanAsync(plan);
                if (!success)
                {
                    return BadRequest(new { success = false, message = "更新订阅计划失败" });
                }

                return Ok(new { success = true, message = "订阅计划已更新" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新订阅计划时发生错误: {PlanId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 删除订阅计划（仅限SuperAdmin）
        /// </summary>
        [HttpDelete("subscription-plans/{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteSubscriptionPlan(Guid id)
        {
            try
            {
                _logger.LogInformation("删除订阅计划: {PlanId}", id);
                
                // 删除订阅计划
                var success = await _tenantService.DeleteSubscriptionPlanAsync(id);
                if (!success)
                {
                    return BadRequest(new { success = false, message = "删除订阅计划失败，可能有租户正在使用此计划" });
                }

                return Ok(new { success = true, message = "订阅计划已删除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除订阅计划时发生错误: {PlanId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新租户的订阅计划
        /// </summary>
        [HttpPut("{id}/subscription")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> UpdateTenantSubscription(Guid id, [FromBody] UpdateSubscriptionRequest request)
        {
            try
            {
                _logger.LogInformation("更新租户的订阅计划: {TenantId}, {PlanName}, {ExpiryDate}", id, request.PlanName, request.ExpiryDate);
                
                // 更新租户的订阅计划
                var success = await _tenantService.UpdateTenantSubscriptionAsync(id, request.PlanName, request.ExpiryDate);
                if (!success)
                {
                    return BadRequest(new { success = false, message = "更新租户订阅计划失败" });
                }

                return Ok(new { success = true, message = "租户订阅计划已更新" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租户订阅计划时发生错误: {TenantId}", id);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        [HttpGet("check-domain")]
        public async Task<IActionResult> CheckDomainAvailability([FromQuery] string domain, [FromQuery] Guid? excludeTenantId = null)
        {
            try
            {
                _logger.LogInformation("检查域名是否可用: {Domain}, 排除租户: {ExcludeTenantId}", domain, excludeTenantId);
                
                // 检查域名是否可用
                var isAvailable = await _tenantService.IsDomainAvailableAsync(domain, excludeTenantId);
                
                return Ok(new { isAvailable });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名是否可用时发生错误: {Domain}", domain);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }
    }

    /// <summary>
    /// 更新订阅请求
    /// </summary>
    public class UpdateSubscriptionRequest
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        public required string PlanName { get; set; }

        /// <summary>
        /// 过期日期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }
    }
}

using MolySite.Web.Dtos;
using MolySite.Web.Models;
using System.Threading.Tasks;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 认证服务接口，处理用户注册、登录等操作
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 用户注册
        /// </summary>
        Task<AuthResult> RegisterAsync(RegisterDto registerDto);

        /// <summary>
        /// 登录
        /// </summary>
        Task<LoginResult> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// 保存认证令牌
        /// </summary>
        Task SaveAuthTokens(LoginResponseDto loginResponse);

        /// <summary>
        /// 注销
        /// </summary>
        Task LogoutAsync();

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        Task<LoginResponseDto?> GetCurrentUserAsync();

        /// <summary>
        /// 获取认证详情
        /// </summary>
        Task<AuthDetails> GetAuthDetails();

        /// <summary>
        /// 刷新认证令牌
        /// </summary>
        Task<bool> RefreshTokenAsync();

        /// <summary>
        /// 检查认证状态
        /// </summary>
        Task<bool> IsAuthenticatedAsync();

        /// <summary>
        /// 检查用户是否有指定角色
        /// </summary>
        Task<bool> IsInRoleAsync(string role);

        /// <summary>
        /// 获取默认租户
        /// </summary>
        Task<AuthTenantDto?> GetDefaultTenantAsync();
    }
} 
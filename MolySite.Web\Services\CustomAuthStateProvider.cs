using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.Extensions.Logging;
using MolySite.Web.Dtos;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.JSInterop;
using Microsoft.Extensions.DependencyInjection;
using System.Threading;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 自定义身份验证状态提供程序
    /// </summary>
    public class CustomAuthStateProvider : AuthenticationStateProvider
    {
        private readonly IJSInteropService _jsInteropService;
        private readonly ILogger<CustomAuthStateProvider> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IHttpContextAccessor _httpContextAccessor;
        
        // 缓存认证状态，避免频繁访问会话存储
        private AuthenticationState? _cachedAuthState;
        private DateTime _cacheExpiry = DateTime.MinValue;
        private readonly TimeSpan _cacheDuration = TimeSpan.FromMinutes(1); // 缩短缓存时间以提高安全性

        public CustomAuthStateProvider(
            ILoggerFactory loggerFactory,
            IJSInteropService jsInteropService,
            IServiceProvider serviceProvider,
            IHttpContextAccessor httpContextAccessor) 
        {
            _jsInteropService = jsInteropService;
            _logger = loggerFactory.CreateLogger<CustomAuthStateProvider>();
            _serviceProvider = serviceProvider;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 获取身份验证状态
        /// </summary>
        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            if (_cachedAuthState != null && DateTime.UtcNow < _cacheExpiry)
            {
                _logger.LogInformation("从缓存返回认证状态");
                return _cachedAuthState;
            }

            _logger.LogInformation("开始获取认证状态...");

            var httpContext = _httpContextAccessor.HttpContext;

            // 优先尝试从HttpContext获取认证信息 (适用于Blazor Server初始加载)
            if (httpContext?.User.Identity?.IsAuthenticated ?? false)
            {
                _logger.LogInformation("从HttpContext检测到已认证用户: {UserName}, 角色: {Roles}",
                    httpContext.User.Identity.Name,
                    string.Join(", ", httpContext.User.Claims
                        .Where(c => c.Type == ClaimTypes.Role)
                        .Select(c => c.Value)));

                var principal = httpContext.User;
                var authState = new AuthenticationState(principal);

                // 缓存认证状态
                _cachedAuthState = authState;
                _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);

                return authState;
            }

            // 如果HttpContext中没有，则尝试从本地存储获取 (适用于Blazor WebAssembly或已建立连接的Server)
            _logger.LogInformation("HttpContext中无认证用户，尝试从本地存储获取");

            try
            {
                // 检查是否处于预渲染阶段
                bool isJsAvailable = false;
                try
                {
                    isJsAvailable = await _jsInteropService.CanInvokeJSMethod();
                }
                catch
                {
                    isJsAvailable = false;
                }
                
                if (!isJsAvailable)
                {
                    _logger.LogInformation("预渲染期间，JavaScript交互不可用");
                    
                    // 尝试从DI获取HttpContext，检查Cookie
                    using var scope = _serviceProvider.CreateScope();
                    var httpContextAccessor = scope.ServiceProvider.GetService<IHttpContextAccessor>();
                    
                    if (httpContextAccessor?.HttpContext != null)
                    {
                        _logger.LogInformation("尝试从HttpContext获取认证信息");
                        
                        // 检查认证Cookie
                        if (httpContextAccessor.HttpContext.User.Identity?.IsAuthenticated == true)
                        {
                            _logger.LogInformation("从HttpContext获取到已认证用户: {UserName}", 
                                httpContextAccessor.HttpContext.User.Identity.Name);
                            
                            var httpAuthState = new AuthenticationState(httpContextAccessor.HttpContext.User);
                            _cachedAuthState = httpAuthState;
                            _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);
                            return httpAuthState;
                        }
                        
                        // 检查自定义Cookie
                        if (httpContextAccessor.HttpContext.Request.Cookies.TryGetValue("MolySite.AuthUser", out var username) &&
                            httpContextAccessor.HttpContext.Request.Cookies.TryGetValue("MolySite.AuthRole", out var roleString))
                        {
                            _logger.LogInformation("从自定义Cookie获取到用户信息: {UserName}, 角色: {Roles}", 
                                username, roleString);
                            
                            var roles = roleString.Split(',', StringSplitOptions.RemoveEmptyEntries);
                            var identity = new ClaimsIdentity(new[]
                            {
                                new Claim(ClaimTypes.Name, username),
                            }, "MolySite.Cookie");
                            
                            foreach (var role in roles)
                            {
                                identity.AddClaim(new Claim(ClaimTypes.Role, role.Trim()));
                            }
                            
                            var user = new ClaimsPrincipal(identity);
                            var cookieAuthState = new AuthenticationState(user);
                            _cachedAuthState = cookieAuthState;
                            _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);
                            return cookieAuthState;
                        }
                    }
                    
                    _logger.LogInformation("预渲染期间，JavaScript交互不可用，返回匿名用户");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                // 从本地存储获取用户信息
                var userJson = await _jsInteropService.GetLocalStorage("user");
                
                if (string.IsNullOrEmpty(userJson))
                {
                    _logger.LogInformation("未找到用户信息，返回匿名用户");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                _logger.LogInformation("从本地存储获取到用户信息JSON: {UserJson}", userJson);

                // 检查authToken是否存在
                var authToken = await _jsInteropService.GetLocalStorage("authToken");
                _logger.LogInformation("从本地存储获取到认证令牌: {HasToken}", !string.IsNullOrEmpty(authToken));

                // 反序列化用户信息
                var loginResponse = JsonSerializer.Deserialize<LoginResponseDto>(userJson, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                
                if (loginResponse == null)
                {
                    _logger.LogWarning("用户信息反序列化失败，返回匿名用户");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                _logger.LogInformation("反序列化用户信息成功: {UserName}, 角色: {Roles}, 令牌长度: {TokenLength}", 
                    loginResponse.UserName, 
                    string.Join(", ", loginResponse.Roles ?? new List<string>()),
                    loginResponse.Token?.Length ?? 0);

                // 验证必要的认证信息
                if (string.IsNullOrEmpty(loginResponse.Token) && string.IsNullOrEmpty(authToken))
                {
                    _logger.LogWarning("没有有效的认证令牌，返回匿名用户");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                // 确保Token不为空
                if (string.IsNullOrEmpty(loginResponse.Token) && !string.IsNullOrEmpty(authToken))
                {
                    loginResponse.Token = authToken;
                }

                // 确保Roles不为null
                if (loginResponse.Roles == null)
                {
                    loginResponse.Roles = new List<string>();
                }

                // 验证关键信息完整性
                if (loginResponse.UserId == Guid.Empty ||
                    string.IsNullOrEmpty(loginResponse.UserName) ||
                    string.IsNullOrEmpty(loginResponse.Email))
                {
                    _logger.LogWarning("认证信息不完整，返回匿名用户");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                // 创建认证状态
                var resultAuthState = CreateAuthenticationState(loginResponse);
                
                // 缓存认证状态
                _cachedAuthState = resultAuthState;
                _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);
                
                return resultAuthState;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取身份验证状态时发生错误");
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        /// <summary>
        /// 标记用户为已认证
        /// </summary>
        public void MarkUserAsAuthenticated(LoginResponseDto loginResponse)
        {
            _logger.LogInformation("标记用户为已认证: {UserName}, 角色: {Roles}", 
                loginResponse.UserName, string.Join(", ", loginResponse.Roles ?? new List<string>()));
            
            // 确保Roles不为null
            if (loginResponse.Roles == null)
            {
                loginResponse.Roles = new List<string>();
                _logger.LogWarning("用户角色为null，已初始化为空列表");
            }
            
            var authState = CreateAuthenticationState(loginResponse);
            _cachedAuthState = authState;
            _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);
            
            NotifyAuthenticationStateChanged(Task.FromResult(authState));
        }

        /// <summary>
        /// 标记用户为已注销
        /// </summary>
        public void MarkUserAsLoggedOut()
        {
            _logger.LogInformation("标记用户为已注销");
            
            var anonymousUser = new ClaimsPrincipal(new ClaimsIdentity());
            var authState = new AuthenticationState(anonymousUser);
            
            _cachedAuthState = authState;
            _cacheExpiry = DateTime.UtcNow.Add(_cacheDuration);
            
            NotifyAuthenticationStateChanged(Task.FromResult(authState));
        }
        
        /// <summary>
        /// 强制刷新认证状态
        /// </summary>
        public void ForceRefresh()
        {
            _cachedAuthState = null;
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }

        // 创建认证状态
        private AuthenticationState CreateAuthenticationState(LoginResponseDto loginResponse)
        {
            // 创建声明
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, loginResponse.UserId.ToString()),
                new Claim(ClaimTypes.Name, loginResponse.UserName!),
                new Claim(ClaimTypes.Email, loginResponse.Email!)
            };

            // 添加角色声明
            if (loginResponse.Roles != null && loginResponse.Roles.Any())
            {
                foreach (var role in loginResponse.Roles)
                {
                    _logger.LogInformation("添加角色声明: {Role}", role);
                    claims.Add(new Claim(ClaimTypes.Role, role));
                }
            }
            else
            {
                _logger.LogWarning("用户没有角色信息");
            }

            // 创建身份
            var identity = new ClaimsIdentity(claims, "MolySite.Authentication");
            
            // 创建用户主体
            var user = new ClaimsPrincipal(identity);
            
            // 创建认证状态
            return new AuthenticationState(user);
        }
    }
} 
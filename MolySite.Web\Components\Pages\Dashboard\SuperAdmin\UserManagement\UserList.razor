@page "/dashboard/superadmin/usermanagement"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject IUserService UserService
@inject NavigationManager NavigationManager
@inject ILogger<UserList> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>用户管理 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">用户管理</h1>
        <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/superadmin/usermanagement/create"))" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
            创建用户
        </button>
    </div>

    <div class="bg-white shadow rounded-lg p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-[200px]">
                <label for="searchTerm" class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <input type="text" id="searchTerm" @bind="SearchTerm" @bind:event="oninput" @onkeyup="HandleSearch"
                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                       placeholder="用户名、邮箱或姓名" />
            </div>
            <div class="w-full md:w-auto">
                <label for="roleFilter" class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                <select id="roleFilter" @bind="SelectedRole" 
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">所有角色</option>
                    <option value="SuperAdmin">超级管理员</option>
                    <option value="TenantAdmin">租户管理员</option>
                    <option value="TenantUser">租户用户</option>
                </select>
            </div>
            <div class="w-full md:w-auto">
                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select id="statusFilter" @bind="SelectedStatus" 
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">禁用</option>
                </select>
            </div>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_filteredUsers == null || !_filteredUsers.Any())
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">暂无用户数据</p>
        </div>
    }
    else
    {
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            用户信息
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            角色
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            租户
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            最后登录
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var user in _filteredUsers)
                    {
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">@user.UserName</div>
                                        <div class="text-sm text-gray-500">@user.Email</div>
                                        @if (!string.IsNullOrEmpty(user.FullName))
                                        {
                                            <div class="text-sm text-gray-500">@user.FullName</div>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                       @(user.Role == "SuperAdmin" ? "bg-purple-100 text-purple-800" :
                                         user.Role == "Admin" ? "bg-orange-100 text-orange-800" :
                                         user.Role == "TenantAdmin" ? "bg-blue-100 text-blue-800" :
                                         "bg-green-100 text-green-800")">
                                    @GetRoleDisplayName(user.Role)
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @(string.IsNullOrEmpty(user.TenantName) ? "-" : user.TenantName)
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (user.IsActive)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        活跃
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        禁用
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @(user.LastLoginAt?.ToString("yyyy-MM-dd HH:mm") ?? "-")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button @onclick="@(() => NavigationManager.NavigateTo($"/dashboard/superadmin/usermanagement/edit/{user.Id}"))" 
                                            class="text-indigo-600 hover:text-indigo-900">
                                        编辑
                                    </button>
                                    <button @onclick="@(() => ShowResetPasswordModal(user))" 
                                            class="text-orange-600 hover:text-orange-900">
                                        重置密码
                                    </button>
                                    <button @onclick="@(() => ToggleUserStatus(user))" 
                                            class="@(user.IsActive ? "text-red-600 hover:text-red-900" : "text-green-600 hover:text-green-900")">
                                        @(user.IsActive ? "禁用" : "启用")
                                    </button>
                                    <button @onclick="@(() => ShowDeleteConfirmation(user))" 
                                            class="text-red-600 hover:text-red-900">
                                        删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }

    @if (_showDeleteModal)
    {
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
            <div class="bg-white p-5 rounded-lg shadow-lg max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
                <p class="text-gray-600 mb-6">
                    您确定要删除用户 "@_userToDelete?.UserName" 吗？此操作无法撤销。
                </p>
                <div class="flex justify-end space-x-3">
                    <button @onclick="CancelDelete" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                        取消
                    </button>
                    <button @onclick="ConfirmDelete" 
                            class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded">
                        删除
                    </button>
                </div>
            </div>
        </div>
    }

    @if (_showResetPasswordModal)
    {
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
            <div class="bg-white p-5 rounded-lg shadow-lg max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">重置密码</h3>
                <p class="text-gray-600 mb-4">
                    为用户 "@_userToResetPassword?.UserName" 设置新密码：
                </p>
                <div class="mb-4">
                    <input type="password" @bind="_newPassword" 
                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                           placeholder="新密码" />
                </div>
                <div class="flex justify-end space-x-3">
                    <button @onclick="CancelResetPassword" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
                        取消
                    </button>
                    <button @onclick="ConfirmResetPassword" 
                            class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                        确认重置
                    </button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<UserDto> _users = new();
    private List<UserDto> _filteredUsers = new();
    private bool _loading = true;
    private bool _showDeleteModal = false;
    private UserDto? _userToDelete;
    private bool _showResetPasswordModal = false;
    private UserDto? _userToResetPassword;
    private string _newPassword = "";

    private string SearchTerm { get; set; } = "";
    private string SelectedRole { get; set; } = "";
    private string SelectedStatus { get; set; } = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            _loading = true;
            _users = await UserService.GetAllUsersAsync();
            FilterUsers();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载用户列表时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private void FilterUsers()
    {
        _filteredUsers = _users.Where(u => 
            (string.IsNullOrEmpty(SearchTerm) || 
             u.UserName.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) || 
             u.Email.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) || 
             (u.FullName != null && u.FullName.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase))) &&
            (string.IsNullOrEmpty(SelectedRole) || u.Role == SelectedRole) &&
            (string.IsNullOrEmpty(SelectedStatus) || 
             (SelectedStatus == "active" && u.IsActive) || 
             (SelectedStatus == "inactive" && !u.IsActive))
        ).ToList();
    }

    private void HandleSearch()
    {
        FilterUsers();
    }

    private string GetRoleDisplayName(string role)
    {
        return role switch
        {
            "SuperAdmin" => "超级管理员",
            "Admin" => "管理员",
            "TenantAdmin" => "租户管理员",
            "TenantUser" => "租户用户",
            _ => role
        };
    }

    private void ShowDeleteConfirmation(UserDto user)
    {
        _userToDelete = user;
        _showDeleteModal = true;
    }

    private void CancelDelete()
    {
        _showDeleteModal = false;
        _userToDelete = null;
    }

    private async Task ConfirmDelete()
    {
        if (_userToDelete != null)
        {
            try
            {
                await UserService.DeleteUserAsync(_userToDelete.Id);
                _showDeleteModal = false;
                _userToDelete = null;
                await LoadUsers();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除用户时发生错误");
            }
        }
    }

    private void ShowResetPasswordModal(UserDto user)
    {
        _userToResetPassword = user;
        _newPassword = "";
        _showResetPasswordModal = true;
    }

    private void CancelResetPassword()
    {
        _showResetPasswordModal = false;
        _userToResetPassword = null;
        _newPassword = "";
    }

    private async Task ConfirmResetPassword()
    {
        if (_userToResetPassword != null && !string.IsNullOrWhiteSpace(_newPassword))
        {
            try
            {
                await UserService.ResetPasswordAsync(_userToResetPassword.Id, _newPassword);
                _showResetPasswordModal = false;
                _userToResetPassword = null;
                _newPassword = "";
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "重置用户密码时发生错误");
            }
        }
    }

    private async Task ToggleUserStatus(UserDto user)
    {
        try
        {
            await UserService.ToggleUserStatusAsync(user.Id, !user.IsActive);
            await LoadUsers();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "切换用户状态时发生错误");
        }
    }
}

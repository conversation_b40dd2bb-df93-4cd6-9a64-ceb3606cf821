using System;
using Microsoft.AspNetCore.Identity;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 角色权限映射模型
    /// </summary>
    public class RolePermission
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// 权限名称
        /// </summary>
        public required string Permission { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// 导航属性：所属租户
        /// </summary>
        public Tenant? Tenant { get; set; }
    }
} 
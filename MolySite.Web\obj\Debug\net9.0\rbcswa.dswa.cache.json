{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["cOIXvR5tD3iAOd9DJDnq5w3/3mlZqlS2ZZG4BfICxyA=", "aFg3qZlKl5Yt958qhTMp3W6cckECRHf8PYODdMDOY+I=", "SauHOyieXoAg79BqCu16BJTiv/cWNcQKh6q1Gp3Aops=", "005lWNJCWG9tNK2V83dC3+qFG9T9oPGRah+DQYbFLXo=", "vkVesg+KeDc8XBgrxoWwZjcWN5M4F6QplmxxUSnWXec=", "UamOEaktdSjpGRJ3ihN+DFGrtzWaYGAsAYSClnIcN5o=", "MV2ZzVnPSrarDBlokZ10XmjpxbQF9OnsuRW6okwlMQg=", "iUDRzrMJIo2EmuzgISKtuHDYx6EuDoq23gJ59cfntt0=", "HB0DtcfsCE8cnkliGlzFErE1iGreApDn3HtDN6BMmME=", "Jk2nb1nFaZ87sM6m86aejIGQWsxTfBsEQZPKTvDhL20=", "TxJP6+u5Jc+rMR3VaHwFJ8GHLvqvtRzt5bBA/k7CyUU=", "77Jabq5aVFFWO/KUSo5a8x4EfXN/TDFTtasc+2TScMI=", "BW9CxlUaw4O7NqAL68GW2H/2F6+irb6XqskSUqHyED0=", "rLrgtdbXt3fsc+a7oEZrfaKZ7yxopMHIrjd6ZlP7G+A=", "wF3J6iYvKr6PuycQ1ttJ0U48Y+9hophaFI/0dy3mq20=", "/LF+jqmPwur71yCZxG8C1AFdBt2gJyXkGQmDCiKjs7U=", "/x0JK5E5TboNUWvEtwugVNgCFlXBfFup+hQrg334fHw=", "N+Bh4oh0gdadh+kUC3T1SJeXdnA7emWJkfq9Q1NlrjM=", "2Aet2/ddZwkhC+HcVWGMvRjwZIS8dOxw4gY8Id2oNpk=", "i+OOanvbWELiKQxnuqLgjb4R6x7EHsQDpe6WPrSwtRc=", "MtLCymMiplOciKgKXtun9C6PJauTG0N5Y3/Wn4r7d0A=", "26myvW1O/mrOWSSyhQh+cptUBY1GNpbsUot0ImGEZMc=", "m1WXyuMTp1OHIbqSN01FUZhTv+oRax2AkWM2RjT8ksw=", "tWKAjX1BGv+L/gc0FvE8cJLMSgUVV+OU11CxcBEkGF4=", "rh7/Fgjk0mxQ+gtjy5OMrBZcIINmbdEVA5iFsZzvbkw=", "wbEWTWjw30OMH3I8nF5FTy4fNdSqmidxkrmzXlwtLhs=", "7/qV+yilXCSA+YOAJMWm+tRIlyvYhwaLpj5C92Amok8=", "OxSps6nskGKI1USyatld2KupRQHX0zU2GjdwWXlADrM=", "qYpMOEL1nccRNHrzC7e54Aa+jX0eG397YIxsMxZmS2c=", "8U3G6a4NREqQgN3VfjtnHG6DvbohfCsKNCsu/Ngzpxs=", "LFrAjvQRMvssXL8FebAq8vDb1qJ+sSksfFVGAFAy4I8=", "xzrYk0Y5hFIfbi6dOGiJ9u2cTWIe30Kuf+pfVYTcDDI=", "GwJvv+CGv1Vb/azPjppEAOy4Wk0g5PPn8ED4vs8snoo=", "hjMKTJlQFdOfVHoEwNC1e/NU0KWdaFYJi9Gw14Zo4v0=", "hKA+r5atUxKjZSAa/U1aKTVK1iu4oue/JtXVg4j24XM=", "gv8eNe/zLKTw4fyk41nci8z2fCpo+Vrdu8wHZh4nysI=", "fw7gQXyp9NVHwxbdvPpZF74Pac1mBd0/htt0lo4T5sY=", "Mb13VwfnkFiqnmjUDJUZr5k8xJNbfTbOK/aaWa/qphA=", "MQ4jmJCOsmnSYMXcdDEBkKy0FUgOcs4dNA6+nVvxYtk=", "2ogkKDFKLifFkM3I1dAyq+4xkibSBQb0QT4aKC4XEGM=", "Z2Rl0MbIewrOsBLfgxRvwM4tFlNlZyueJCPcl3X3JdY=", "/tPL7pGwoCNzyXmIYkmb/Zetd7rbHGUsgrty9paTA5w=", "C7+QgPX7GD75ic3rPP+8lHPMLxX8I20JR28ggZKDvto=", "lWfUmE5xQcUsSOAC9f0fMREjIjH9Dbe1xGwdlBXdEGU=", "Hx2xU/a15BmkgFjInXnlutkb4d70uiNe3NFvOxi5Xfw=", "DAWDVkWNO2IHzKlpMAJ/dTZBqvk+/8bc/rHAEWC24vw=", "CF0JpTDcSVmTUhfbqTFUkKdjx39xqiTCet7fzbejDnU=", "4F1ki7Ue+3EdhoIGhiLw9ulToS2psO9aNe4QxiMg/vU=", "TDg+LquGbIawnE4jjPdtDku5dQzFuHjQLxjJtSVDdPA=", "Sv0JI2As23DL51/lgE2/3WuUgQYDK/yhv6dUnjWsZsA="], "CachedAssets": {"Sv0JI2As23DL51/lgE2/3WuUgQYDK/yhv6dUnjWsZsA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\if8keuzty8-ilj1uyfixu.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=ilj1uyfixu}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h0ycb8olwp", "Integrity": "z/0exXKq3YL2tARcZTgYK9p++BvUVSlUYE1/YYaK5J8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MolySite.Web.bundle.scp.css", "FileLength": 1767, "LastWriteTime": "2025-06-17T00:44:51.690007+00:00"}, "DAWDVkWNO2IHzKlpMAJ/dTZBqvk+/8bc/rHAEWC24vw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\d6t48wts2t-63fj8s7r0e.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "Hx2xU/a15BmkgFjInXnlutkb4d70uiNe3NFvOxi5Xfw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\xdox1novht-h1s4sie4z3.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "lWfUmE5xQcUsSOAC9f0fMREjIjH9Dbe1xGwdlBXdEGU=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\syc8a72p2b-notf2xhcfb.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "C7+QgPX7GD75ic3rPP+8lHPMLxX8I20JR28ggZKDvto=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ccyhwz0y0i-y7v9cxd14o.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "/tPL7pGwoCNzyXmIYkmb/Zetd7rbHGUsgrty9paTA5w=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\e2fy7z35iw-jj8uyg4cgr.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "Z2Rl0MbIewrOsBLfgxRvwM4tFlNlZyueJCPcl3X3JdY=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\xoehaubqwe-kbrnm935zg.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "2ogkKDFKLifFkM3I1dAyq+4xkibSBQb0QT4aKC4XEGM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\7wycym7oia-vr1egmr9el.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "MQ4jmJCOsmnSYMXcdDEBkKy0FUgOcs4dNA6+nVvxYtk=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\wejz3bqr3b-iovd86k7lj.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "Mb13VwfnkFiqnmjUDJUZr5k8xJNbfTbOK/aaWa/qphA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\xz5x5f9hi1-493y06b0oq.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "fw7gQXyp9NVHwxbdvPpZF74Pac1mBd0/htt0lo4T5sY=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\x4klfxl2h7-6pdc2jztkx.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "gv8eNe/zLKTw4fyk41nci8z2fCpo+Vrdu8wHZh4nysI=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\wo34887tk8-6cfz1n2cew.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "hKA+r5atUxKjZSAa/U1aKTVK1iu4oue/JtXVg4j24XM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\77cz5b1z6t-ft3s53vfgj.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "hjMKTJlQFdOfVHoEwNC1e/NU0KWdaFYJi9Gw14Zo4v0=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\cfqxzzuttn-pk9g2wxc8p.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "GwJvv+CGv1Vb/azPjppEAOy4Wk0g5PPn8ED4vs8snoo=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\n0crhvam9m-hrwsygsryq.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "xzrYk0Y5hFIfbi6dOGiJ9u2cTWIe30Kuf+pfVYTcDDI=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ug9dmemwmu-37tfw0ft22.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "LFrAjvQRMvssXL8FebAq8vDb1qJ+sSksfFVGAFAy4I8=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\j8pyepajpt-v0zj4ognzu.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "8U3G6a4NREqQgN3VfjtnHG6DvbohfCsKNCsu/Ngzpxs=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\2mdf19xwt3-46ein0sx1k.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "qYpMOEL1nccRNHrzC7e54Aa+jX0eG397YIxsMxZmS2c=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ifogsbrj92-pj5nd1wqec.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "OxSps6nskGKI1USyatld2KupRQHX0zU2GjdwWXlADrM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\7huzj2bmzn-s35ty4nyc5.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "7/qV+yilXCSA+YOAJMWm+tRIlyvYhwaLpj5C92Amok8=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\bijtgm8lkf-nvvlpmu67g.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "wbEWTWjw30OMH3I8nF5FTy4fNdSqmidxkrmzXlwtLhs=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\2e08am4nz0-06098lyss8.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "rh7/Fgjk0mxQ+gtjy5OMrBZcIINmbdEVA5iFsZzvbkw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\aj672zgjpz-j5mq2jizvt.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "tWKAjX1BGv+L/gc0FvE8cJLMSgUVV+OU11CxcBEkGF4=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\w2n934gzpv-tdbxkamptv.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "m1WXyuMTp1OHIbqSN01FUZhTv+oRax2AkWM2RjT8ksw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\0hsvhssxox-c2oey78nd0.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "26myvW1O/mrOWSSyhQh+cptUBY1GNpbsUot0ImGEZMc=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\pn7smc1w7c-lcd1t2u6c8.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "MtLCymMiplOciKgKXtun9C6PJauTG0N5Y3/Wn4r7d0A=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9dt6tfabif-r4e9w2rdcm.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "i+OOanvbWELiKQxnuqLgjb4R6x7EHsQDpe6WPrSwtRc=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\2rw25w61re-khv3u5hwcm.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "2Aet2/ddZwkhC+HcVWGMvRjwZIS8dOxw4gY8Id2oNpk=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\2ynz4wjlzm-jd9uben2k1.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "N+Bh4oh0gdadh+kUC3T1SJeXdnA7emWJkfq9Q1NlrjM=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\2eow5t2uja-dxx9fxp4il.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "/x0JK5E5TboNUWvEtwugVNgCFlXBfFup+hQrg334fHw=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\gvfj9fr1fv-ee0r1s7dh0.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "/LF+jqmPwur71yCZxG8C1AFdBt2gJyXkGQmDCiKjs7U=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\tp5lvny8b0-rzd6atqjts.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "wF3J6iYvKr6PuycQ1ttJ0U48Y+9hophaFI/0dy3mq20=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\1ptmlgsiyz-fsbi9cje9m.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "rLrgtdbXt3fsc+a7oEZrfaKZ7yxopMHIrjd6ZlP7G+A=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\syww5u996f-b7pk76d08c.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "BW9CxlUaw4O7NqAL68GW2H/2F6+irb6XqskSUqHyED0=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\1tqowutgt5-fvhpjtyr6v.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "77Jabq5aVFFWO/KUSo5a8x4EfXN/TDFTtasc+2TScMI=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\7sd15m6qy4-ub07r2b239.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "TxJP6+u5Jc+rMR3VaHwFJ8GHLvqvtRzt5bBA/k7CyUU=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\87irtjz28f-cosvhxvwiu.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "Jk2nb1nFaZ87sM6m86aejIGQWsxTfBsEQZPKTvDhL20=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\mptiy5vbb8-k8d9w2qqmf.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "HB0DtcfsCE8cnkliGlzFErE1iGreApDn3HtDN6BMmME=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\3yxwis00ei-ausgxo2sd3.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "iUDRzrMJIo2EmuzgISKtuHDYx6EuDoq23gJ59cfntt0=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\13lh6wbstw-d7shbmvgxk.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "MV2ZzVnPSrarDBlokZ10XmjpxbQF9OnsuRW6okwlMQg=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\q1kpm3ilpn-aexeepp0ev.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "UamOEaktdSjpGRJ3ihN+DFGrtzWaYGAsAYSClnIcN5o=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\hndzd7rqdk-erw9l3u2r3.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "vkVesg+KeDc8XBgrxoWwZjcWN5M4F6QplmxxUSnWXec=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\e7wbsebqvs-c2jlpeoesf.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "005lWNJCWG9tNK2V83dC3+qFG9T9oPGRah+DQYbFLXo=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\72oq6fbip9-bqjiyaj88i.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "SauHOyieXoAg79BqCu16BJTiv/cWNcQKh6q1Gp3Aops=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9qndxilbbr-26f9b7qkas.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap-icons/font/bootstrap-icons.min#[.{fingerprint=26f9b7qkas}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6um1ts2axh", "Integrity": "IglclAz7SLu0YmGB7PxVX8owYskkCvVdjd0rSP0X+mw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap-icons\\font\\bootstrap-icons.min.css", "FileLength": 13894, "LastWriteTime": "2025-06-16T06:55:26+00:00"}, "aFg3qZlKl5Yt958qhTMp3W6cckECRHf8PYODdMDOY+I=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\qftk4dc618-5ipweew5fc.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "css/dashboard#[.{fingerprint=5ipweew5fc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\css\\dashboard.css", "FileLength": 0, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "cOIXvR5tD3iAOd9DJDnq5w3/3mlZqlS2ZZG4BfICxyA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\4yau8vpo2c-khy4lop6wu.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "app#[.{fingerprint=khy4lop6wu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "shxmjhlez9", "Integrity": "8AW5z0bh7/mw0xH4LdDSG9tn9KuE8navaRG23Xkbe7o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\app.css", "FileLength": 1530, "LastWriteTime": "2025-06-17T08:23:01.0460073+00:00"}, "TDg+LquGbIawnE4jjPdtDku5dQzFuHjQLxjJtSVDdPA=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\ll5cbn73iy-ilj1uyfixu.gz", "SourceId": "MolySite.Web", "SourceType": "Computed", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "MolySite.Web#[.{fingerprint=ilj1uyfixu}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h0ycb8olwp", "Integrity": "z/0exXKq3YL2tARcZTgYK9p++BvUVSlUYE1/YYaK5J8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MolySite.Web.styles.css", "FileLength": 1767, "LastWriteTime": "2025-06-17T00:44:51.6890085+00:00"}, "CF0JpTDcSVmTUhfbqTFUkKdjx39xqiTCet7fzbejDnU=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\9nme7l4e04-0j3bgjxly4.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-16T06:53:18+00:00"}, "4F1ki7Ue+3EdhoIGhiLw9ulToS2psO9aNe4QxiMg/vU=": {"Identity": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\yygdecoiyq-1woriag5be.gz", "SourceId": "MolySite.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MolySite.Web", "RelativePath": "js/app#[.{fingerprint=1woriag5be}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g0kkyx7mtu", "Integrity": "sOeChRW8lYnyaPqTaO60b+sXKQVK/e7G4S4v1t3pXPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Project-SaaS\\MolySite\\MolySite.Web\\wwwroot\\js\\app.js", "FileLength": 443, "LastWriteTime": "2025-06-17T08:23:01.0460073+00:00"}}, "CachedCopyCandidates": {}}
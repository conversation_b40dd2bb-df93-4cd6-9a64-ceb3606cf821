using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Data;
using MolySite.Identity.Models;
using System.Security.Claims;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 多租户授权服务实现
    /// </summary>
    public class AuthorizationService : IAuthorizationService
    {
        private readonly ApplicationDbContext _context;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;
        private readonly UserManager<ApplicationUser> _userManager;

        public AuthorizationService(
            ApplicationDbContext context,
            RoleManager<IdentityRole<Guid>> roleManager,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _roleManager = roleManager;
            _userManager = userManager;
        }

        /// <summary>
        /// 添加角色
        /// </summary>
        public async Task<bool> AddRoleAsync(string roleName, Guid tenantId)
        {
            var role = new IdentityRole<Guid>
            {
                Name = roleName,
                NormalizedName = roleName.ToUpper()
            };

            var result = await _roleManager.CreateAsync(role);
            return result.Succeeded;
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        public async Task<bool> RemoveRoleAsync(string roleName, Guid tenantId)
        {
            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null)
            {
                return false;
            }

            var result = await _roleManager.DeleteAsync(role);
            return result.Succeeded;
        }

        /// <summary>
        /// 为角色分配权限
        /// </summary>
        public async Task<bool> AssignPermissionsToRoleAsync(string roleName, List<string> permissions, Guid tenantId)
        {
            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null)
            {
                return false;
            }

            // 删除现有权限
            await _context.RolePermissions
                .Where(rp => rp.RoleId == role.Id)
                .ExecuteDeleteAsync();

            // 添加新权限
            var rolePermissions = permissions.Select(permission => new Models.RolePermission
            {
                RoleId = role.Id,
                Permission = permission,
                TenantId = tenantId
            }).ToList();

            _context.RolePermissions.AddRange(rolePermissions);
            await _context.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// 获取角色权限
        /// </summary>
        public async Task<List<string>> GetRolePermissionsAsync(string roleName, Guid tenantId)
        {
            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null)
            {
                return new List<string>();
            }

            return await _context.RolePermissions
                .Where(rp => rp.RoleId == role.Id && rp.TenantId == tenantId)
                .Select(rp => rp.Permission)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<bool> HasPermissionAsync(ClaimsPrincipal user, string permission)
        {
            var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
                return false;

            var parsedUserId = Guid.Parse(userId);
            var tenantId = user.FindFirstValue("TenantId");
            if (string.IsNullOrEmpty(tenantId))
                return false;

            var parsedTenantId = Guid.Parse(tenantId);

            // 查询用户的角色权限
            var rolePermissions = await _context.RolePermissions
                .Where(rp => rp.TenantId == parsedTenantId)
                .Select(rp => rp.Permission)
                .ToListAsync();

            return rolePermissions.Contains(permission);
        }

        /// <inheritdoc/>
        public async Task<bool> HasPermissionAsync(Guid userId, string permission)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null)
                return false;

            // 查询用户的角色权限
            var rolePermissions = await _context.RolePermissions
                .Where(rp => rp.TenantId == user.TenantId)
                .Select(rp => rp.Permission)
                .ToListAsync();

            return rolePermissions.Contains(permission);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<string>> GetUserPermissionsAsync(ClaimsPrincipal user)
        {
            var appUser = await _userManager.GetUserAsync(user);
            return appUser?.Permissions ?? Enumerable.Empty<string>();
        }

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        public async Task<bool> AssignRoleToUserAsync(Guid userId, string roleName)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                return false;
            }

            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null)
            {
                return false;
            }

            var result = await _userManager.AddToRoleAsync(user, roleName);
            return result.Succeeded;
        }

        public async Task<bool> AddRolePermissionsAsync(Guid roleId, Guid tenantId, string[] permissions)
        {
            // 删除现有权限
            await _context.RolePermissions
                .Where(rp => rp.RoleId == roleId)
                .ExecuteDeleteAsync();

            // 创建新的角色权限
            var rolePermissions = permissions.Select(permission => new Models.RolePermission
            {
                RoleId = roleId,
                Permission = permission,
                TenantId = tenantId
            }).ToList();

            _context.RolePermissions.AddRange(rolePermissions);
            await _context.SaveChangesAsync();

            return true;
        }
    }
} 
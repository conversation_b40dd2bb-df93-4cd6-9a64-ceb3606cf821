using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Identity;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 应用用户模型，支持多租户和细粒度权限
    /// </summary>
    public class ApplicationUser : IdentityUser<Guid>
    {
        /// <summary>
        /// 覆盖基类的 UserName 属性
        /// </summary>
        public override string UserName { get; set; } = null!;

        /// <summary>
        /// 角色（别名为 Roles）
        /// </summary>
        public List<string> Roles => UserRoles;

        /// <summary>
        /// 所属租户ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// 导航属性：所属租户
        /// </summary>
        public required Tenant Tenant { get; set; }

        /// <summary>
        /// 用户角色
        /// </summary>
        public List<string> UserRoles { get; set; } = new List<string>();

        /// <summary>
        /// 用户权限
        /// </summary>
        public List<string> Permissions { get; set; } = new List<string>();

        /// <summary>
        /// 账户是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 密码重置令牌
        /// </summary>
        public string? PasswordResetToken { get; set; }

        /// <summary>
        /// 密码重置令牌过期时间
        /// </summary>
        public DateTime? PasswordResetTokenExpiration { get; set; }
    }
} 
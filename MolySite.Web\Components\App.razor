﻿<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="lib/bootstrap-icons/font/bootstrap-icons.min.css" />
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="MolySite.Web.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet />
</head>

<body>
    <Routes />
    <script src="_framework/blazor.web.js"></script>
    <script src="lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>

</html>

@code {
    [Inject]
    private ILogger<App> Logger { get; set; } = default!;
    
    protected override void OnInitialized()
    {
        Logger.LogInformation("App组件初始化");
    }
}

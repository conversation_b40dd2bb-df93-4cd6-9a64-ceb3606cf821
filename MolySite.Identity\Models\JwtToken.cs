using System;
using System.Collections.Generic;
using System.Security.Claims;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// JWT 令牌模型，支持多租户和细粒度权限
    /// </summary>
    public class JwtToken
    {
        /// <summary>
        /// 令牌字符串
        /// </summary>
        public required string Token { get; set; }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public required string RefreshToken { get; set; }

        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime Expiration { get; set; }

        /// <summary>
        /// 生成 JWT 声明
        /// </summary>
        public static List<Claim> GenerateClaims(ApplicationUser user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim("TenantId", user.TenantId.ToString())
            };

            // 添加角色声明
            foreach (var role in user.Roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // 添加权限声明
            foreach (var permission in user.Permissions)
            {
                claims.Add(new Claim("Permission", permission));
            }

            return claims;
        }
    }
} 
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MolySite.Identity.Authorization;
using MolySite.Identity.Models;

namespace MolySite.Identity.Data
{
    /// <summary>
    /// 数据种子类，用于初始化租户、角色和权限
    /// </summary>
    public static class DataSeeder
    {
        /// <summary>
        /// 种子数据初始化方法
        /// </summary>
        public static async Task SeedDataAsync(
            ApplicationDbContext context, 
            UserManager<ApplicationUser> userManager, 
            RoleManager<IdentityRole<Guid>> roleManager)
        {
            // 确保数据库已创建
            await context.Database.MigrateAsync();

            // 种子订阅计划数据
            await SeedSubscriptionPlansAsync(context);

            // 种子租户数据
            await SeedTenantsAsync(context);

            // 种子角色数据
            await SeedRolesAsync(roleManager);

            // 种子管理员用户
            await SeedAdminUserAsync(context, userManager);

            // 种子网站配置数据
            await SeedWebsiteConfigsAsync(context);
        }

        /// <summary>
        /// 种子订阅计划数据
        /// </summary>
        private static async Task SeedSubscriptionPlansAsync(ApplicationDbContext context)
        {
            if (!await context.SubscriptionPlans.AnyAsync())
            {
                var plans = new List<SubscriptionPlan>
                {
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Free",
                        Description = "适用于个人或小型项目的免费计划",
                        MonthlyPrice = 0,
                        YearlyPrice = 0,
                        MaxUsers = 5,
                        MaxStorageMB = 100,
                        SupportsCustomDomain = false,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = true,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Basic",
                        Description = "适用于小型团队的基础计划",
                        MonthlyPrice = 9.99m,
                        YearlyPrice = 99.99m,
                        MaxUsers = 10,
                        MaxStorageMB = 500,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = false,
                        SupportsApiAccess = false,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Premium",
                        Description = "适用于中型团队的高级计划",
                        MonthlyPrice = 29.99m,
                        YearlyPrice = 299.99m,
                        MaxUsers = 50,
                        MaxStorageMB = 2048,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = false,
                        IsDefault = false,
                        IsActive = true
                    },
                    new SubscriptionPlan
                    {
                        Id = Guid.NewGuid(),
                        Name = "Enterprise",
                        Description = "适用于大型企业的企业级计划",
                        MonthlyPrice = 99.99m,
                        YearlyPrice = 999.99m,
                        MaxUsers = 200,
                        MaxStorageMB = 10240,
                        SupportsCustomDomain = true,
                        SupportsAdvancedThemes = true,
                        SupportsApiAccess = true,
                        SupportsPrioritySupport = true,
                        IsDefault = false,
                        IsActive = true
                    }
                };

                context.SubscriptionPlans.AddRange(plans);
                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 种子租户数据
        /// </summary>
        private static async Task SeedTenantsAsync(ApplicationDbContext context)
        {
            if (!await context.Tenants.AnyAsync())
            {
                // 获取默认订阅计划
                var freePlan = await context.SubscriptionPlans.FirstOrDefaultAsync(p => p.Name == "Free");
                var freePlanName = freePlan?.Name ?? "Free";

                var tenants = new List<Tenant>
                {
                    new Tenant
                    {
                        Id = Guid.NewGuid(),
                        Name = "默认租户",
                        Domain = "default.molysite.com",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        Description = "系统默认租户",
                        OwnerUserId = Guid.Empty, // 将在创建SuperAdmin用户后更新
                        SubscriptionPlan = freePlanName,
                        SubscriptionExpiresAt = DateTime.UtcNow.AddYears(100), // 默认租户永不过期
                        MaxUsers = freePlan?.MaxUsers ?? 5,
                        MaxStorageMB = freePlan?.MaxStorageMB ?? 100,
                        Theme = "Default"
                    },
                    new Tenant
                    {
                        Id = Guid.NewGuid(),
                        Name = "演示租户",
                        Domain = "demo.molysite.com",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        Description = "演示用途的租户",
                        OwnerUserId = Guid.Empty, // 将在创建Admin用户后更新
                        SubscriptionPlan = freePlanName,
                        SubscriptionExpiresAt = DateTime.UtcNow.AddYears(1),
                        MaxUsers = freePlan?.MaxUsers ?? 5,
                        MaxStorageMB = freePlan?.MaxStorageMB ?? 100,
                        Theme = "Default"
                    }
                };

                context.Tenants.AddRange(tenants);
                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 种子网站配置数据
        /// </summary>
        private static async Task SeedWebsiteConfigsAsync(ApplicationDbContext context)
        {
            // 检查是否已有网站配置
            if (!await context.WebsiteConfigs.AnyAsync())
            {
                // 获取所有租户
                var tenants = await context.Tenants.ToListAsync();

                foreach (var tenant in tenants)
                {
                    var websiteConfig = new WebsiteConfig
                    {
                        Id = Guid.NewGuid(),
                        TenantId = tenant.Id,
                        SiteTitle = $"{tenant.Name} 网站",
                        SiteDescription = $"{tenant.Name} 的官方网站",
                        SiteKeywords = $"{tenant.Name},MolySite,网站",
                        PrimaryColor = "#3b82f6",
                        SecondaryColor = "#10b981",
                        FooterText = $"© {DateTime.Now.Year} {tenant.Name}. 由 MolySite 提供技术支持。",
                        EnableComments = true,
                        EnableRegistration = true,
                        CreatedAt = DateTime.UtcNow
                    };

                    context.WebsiteConfigs.Add(websiteConfig);
                }

                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 种子角色数据
        /// </summary>
        private static async Task SeedRolesAsync(RoleManager<IdentityRole<Guid>> roleManager)
        {
            var roles = new[]
            {
                "SuperAdmin",
                "Admin",
                "Manager",
                "User"
            };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };
                    await roleManager.CreateAsync(role);
                }
            }
        }

        /// <summary>
        /// 种子管理员用户
        /// </summary>
        private static async Task SeedAdminUserAsync(
            ApplicationDbContext context, 
            UserManager<ApplicationUser> userManager)
        {
            // 获取默认租户
            var defaultTenant = await context.Tenants
                .FirstOrDefaultAsync(t => t.Name == "默认租户");

            if (defaultTenant == null)
            {
                throw new InvalidOperationException("默认租户未找到");
            }

            // 检查是否已存在超级管理员用户
            if (await userManager.FindByNameAsync("superadmin") == null)
            {
                var superAdminUser = new ApplicationUser
                {
                    UserName = "superadmin",
                    Email = "<EMAIL>",
                    TenantId = defaultTenant.Id,
                    Tenant = defaultTenant,
                    IsActive = true,
                    Permissions = Permissions.GetSuperAdminPermissions()
                };

                // 单独设置角色
                superAdminUser.UserRoles = new List<string> { "SuperAdmin" };

                var result = await userManager.CreateAsync(superAdminUser, "MolySite@2024!");
                if (result.Succeeded)
                {
                    // 为管理员用户分配角色
                    await userManager.AddToRoleAsync(superAdminUser, "SuperAdmin");

                    // 更新默认租户的所有者ID
                    defaultTenant.OwnerUserId = Guid.Parse(await userManager.GetUserIdAsync(superAdminUser));
                    context.Tenants.Update(defaultTenant);
                    await context.SaveChangesAsync();
                }
            }

            // 获取演示租户
            var demoTenant = await context.Tenants
                .FirstOrDefaultAsync(t => t.Name == "演示租户");

            if (demoTenant != null && await userManager.FindByNameAsync("admin") == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = "admin",
                    Email = "<EMAIL>",
                    TenantId = demoTenant.Id,
                    Tenant = demoTenant,
                    IsActive = true,
                    Permissions = Permissions.GetAdminPermissions()
                };

                // 单独设置角色
                adminUser.UserRoles = new List<string> { "Admin" };

                var result = await userManager.CreateAsync(adminUser, "MolySite@2024!");
                if (result.Succeeded)
                {
                    // 为管理员用户分配角色
                    await userManager.AddToRoleAsync(adminUser, "Admin");

                    // 更新演示租户的所有者ID
                    demoTenant.OwnerUserId = Guid.Parse(await userManager.GetUserIdAsync(adminUser));
                    context.Tenants.Update(demoTenant);
                    await context.SaveChangesAsync();
                }
            }
        }
    }
} 
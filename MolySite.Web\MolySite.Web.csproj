<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Analyzers" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.6" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MolySite.Shared\MolySite.Shared.csproj" />
  </ItemGroup>

</Project>

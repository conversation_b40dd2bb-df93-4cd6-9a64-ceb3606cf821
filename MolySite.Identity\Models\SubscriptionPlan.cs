using System;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 订阅计划模型
    /// </summary>
    public class SubscriptionPlan
    {
        /// <summary>
        /// 订阅计划ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 计划名称（例如：Free, Basic, Premium, Enterprise）
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// 计划描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 计划价格（月）
        /// </summary>
        public decimal MonthlyPrice { get; set; }

        /// <summary>
        /// 计划价格（年）
        /// </summary>
        public decimal YearlyPrice { get; set; }

        /// <summary>
        /// 最大用户数
        /// </summary>
        public int MaxUsers { get; set; }

        /// <summary>
        /// 最大存储空间（MB）
        /// </summary>
        public int MaxStorageMB { get; set; }

        /// <summary>
        /// 是否支持自定义域名
        /// </summary>
        public bool SupportsCustomDomain { get; set; }

        /// <summary>
        /// 是否支持高级主题
        /// </summary>
        public bool SupportsAdvancedThemes { get; set; }

        /// <summary>
        /// 是否支持API访问
        /// </summary>
        public bool SupportsApiAccess { get; set; }

        /// <summary>
        /// 是否支持优先技术支持
        /// </summary>
        public bool SupportsPrioritySupport { get; set; }

        /// <summary>
        /// 是否为系统默认计划
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 计划是否活跃
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }
} 
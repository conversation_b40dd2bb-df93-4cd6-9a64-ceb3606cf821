@page "/dashboard/superadmin/usermanagement/edit/{Id:guid}"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject IUserService UserService
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Edit> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>编辑用户 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <button @onclick="GoBack" class="mr-4 text-gray-600 hover:text-gray-900">
            <i class="bi bi-arrow-left text-xl"></i>
        </button>
        <h1 class="text-2xl font-bold text-gray-800">编辑用户</h1>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else if (_user == null)
    {
        <div class="bg-white shadow rounded-lg p-8 text-center">
            <p class="text-gray-600 text-lg">用户不存在或已被删除</p>
        </div>
    }
    else
    {
        <div class="bg-white shadow rounded-lg p-6">
            <EditForm Model="@_user" OnValidSubmit="HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-600 mb-4" />

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <InputText id="username" @bind-Value="_user.UserName" 
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                        <ValidationMessage For="@(() => _user.UserName)" class="text-red-600 text-sm" />
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                        <InputText id="email" @bind-Value="_user.Email" 
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                        <ValidationMessage For="@(() => _user.Email)" class="text-red-600 text-sm" />
                    </div>

                    <div>
                        <label for="fullname" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                        <InputText id="fullname" @bind-Value="_user.FullName" 
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">电话号码</label>
                        <InputText id="phone" @bind-Value="_user.PhoneNumber" 
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                        <InputSelect id="role" @bind-Value="_user.Role" 
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="SuperAdmin">超级管理员</option>
                            <option value="TenantAdmin">租户管理员</option>
                            <option value="TenantUser">租户用户</option>
                        </InputSelect>
                    </div>

                    <div>
                        <label for="tenant" class="block text-sm font-medium text-gray-700 mb-1">所属租户</label>
                        <InputSelect id="tenant" @bind-Value="_selectedTenantId" 
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">-- 无租户 --</option>
                            @foreach (var tenant in _tenants)
                            {
                                <option value="@tenant.Id">@tenant.Name</option>
                            }
                        </InputSelect>
                    </div>

                    <div class="flex items-center">
                        <InputCheckbox id="isActive" @bind-Value="_user.IsActive" 
                                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                        <label for="isActive" class="ml-2 block text-sm text-gray-900">
                            账户已激活
                        </label>
                    </div>

                    <div class="flex items-center">
                        <InputCheckbox id="emailConfirmed" @bind-Value="_user.EmailConfirmed" 
                                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                        <label for="emailConfirmed" class="ml-2 block text-sm text-gray-900">
                            邮箱已验证
                        </label>
                    </div>
                </div>

                <div class="mt-8 flex justify-end">
                    <button type="button" @onclick="GoBack" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded mr-2">
                        取消
                    </button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                        保存更改
                    </button>
                </div>
            </EditForm>
        </div>
    }
</div>

@code {
    [Parameter]
    public Guid Id { get; set; }

    private UserDto _user;
    private List<TenantDto> _tenants = new();
    private string _selectedTenantId = "";
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            _loading = true;
            
            // 并行加载用户和租户数据
            var userTask = UserService.GetUserByIdAsync(Id);
            var tenantsTask = TenantService.GetAllTenantsAsync();
            
            await Task.WhenAll(userTask, tenantsTask);
            
            _user = await userTask;
            _tenants = await tenantsTask;
            
            if (_user?.TenantId != null)
            {
                _selectedTenantId = _user.TenantId.Value.ToString();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"加载用户 {Id} 数据时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/dashboard/superadmin/usermanagement");
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            if (!string.IsNullOrEmpty(_selectedTenantId) && Guid.TryParse(_selectedTenantId, out Guid tenantId))
            {
                _user.TenantId = tenantId;
                var tenant = _tenants.FirstOrDefault(t => t.Id == tenantId);
                _user.TenantName = tenant?.Name;
            }
            else
            {
                _user.TenantId = null;
                _user.TenantName = null;
            }

            var result = await UserService.UpdateUserAsync(_user);
            if (result)
            {
                NavigationManager.NavigateTo("/dashboard/superadmin/usermanagement");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"更新用户 {Id} 时发生错误");
        }
    }
} 
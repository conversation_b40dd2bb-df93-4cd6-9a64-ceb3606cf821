@page "/dashboard/admin/statistics"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@inject ITenantService TenantService
@inject IUserService UserService
@inject NavigationManager NavigationManager
@inject ILogger<Index> Logger

<PageTitle>租户统计 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">租户统计</h1>
        <div class="flex space-x-2">
            <button @onclick="RefreshData" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                刷新数据
            </button>
            <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/admin"))" 
                    class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded">
                返回仪表盘
            </button>
        </div>
    </div>

    @if (_loading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
    }
    else
    {
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-building text-2xl text-blue-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">我的租户总数</dt>
                                <dd class="text-lg font-medium text-gray-900">@_statistics.TotalTenants</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-check-circle text-2xl text-green-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">活跃租户</dt>
                                <dd class="text-lg font-medium text-gray-900">@_statistics.ActiveTenants</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-people text-2xl text-purple-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">租户用户总数</dt>
                                <dd class="text-lg font-medium text-gray-900">@_statistics.TotalUsers</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-person-check text-2xl text-orange-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                                <dd class="text-lg font-medium text-gray-900">@_statistics.ActiveUsers</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 租户详细信息 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">租户详细信息</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">您管理的租户的详细统计信息</p>
            </div>
            <ul class="divide-y divide-gray-200">
                @foreach (var tenant in _tenants)
                {
                    <li>
                        <div class="px-4 py-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">@tenant.Name.Substring(0, 1).ToUpper()</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">@tenant.Name</div>
                                        <div class="text-sm text-gray-500">@tenant.Domain</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-sm text-gray-500">
                                        <span class="font-medium">订阅:</span> @tenant.SubscriptionPlan
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <span class="font-medium">用户数:</span> @GetTenantUserCount(tenant.Id)
                                    </div>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                           @(tenant.IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800")">
                                        @(tenant.IsActive ? "活跃" : "停用")
                                    </span>
                                </div>
                            </div>
                        </div>
                    </li>
                }
            </ul>
        </div>
    }
</div>

@code {
    private List<TenantDto> _tenants = new();
    private List<UserDto> _users = new();
    private bool _loading = true;
    private AdminStatistics _statistics = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            _loading = true;
            
            // 加载租户和用户数据
            var tenantsTask = TenantService.GetAllTenantsAsync();
            var usersTask = UserService.GetAllUsersAsync();
            
            await Task.WhenAll(tenantsTask, usersTask);
            
            _tenants = tenantsTask.Result;
            _users = usersTask.Result.Where(u => u.Role == "TenantAdmin" || u.Role == "TenantUser").ToList();
            
            // 计算统计信息
            CalculateStatistics();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载统计数据时发生错误");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void CalculateStatistics()
    {
        _statistics.TotalTenants = _tenants.Count;
        _statistics.ActiveTenants = _tenants.Count(t => t.IsActive);
        _statistics.TotalUsers = _users.Count;
        _statistics.ActiveUsers = _users.Count(u => u.IsActive);
    }

    private int GetTenantUserCount(Guid tenantId)
    {
        return _users.Count(u => u.TenantId == tenantId);
    }

    public class AdminStatistics
    {
        public int TotalTenants { get; set; }
        public int ActiveTenants { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
    }
}

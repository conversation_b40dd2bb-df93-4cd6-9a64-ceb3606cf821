// TenantService

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MolySite.Identity.Data;
using MolySite.Identity.Models;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 租户服务实现
    /// </summary>
    public class TenantService : ITenantService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TenantService> _logger;

        public TenantService(
            ApplicationDbContext context,
            ILogger<TenantService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有租户
        /// </summary>
        public async Task<IEnumerable<Tenant>> GetAllTenantsAsync()
        {
            _logger.LogInformation("获取所有租户");
            return await _context.Tenants.ToListAsync();
        }

        /// <summary>
        /// 获取指定租户
        /// </summary>
        public async Task<Tenant?> GetTenantByIdAsync(Guid tenantId)
        {
            _logger.LogInformation("获取租户: {TenantId}", tenantId);
            return await _context.Tenants.FindAsync(tenantId);
        }

        /// <summary>
        /// 获取指定域名的租户
        /// </summary>
        public async Task<Tenant?> GetTenantByDomainAsync(string domain)
        {
            _logger.LogInformation("通过域名获取租户: {Domain}", domain);
            return await _context.Tenants
                .FirstOrDefaultAsync(t => t.Domain == domain);
        }

        /// <summary>
        /// 获取用户所属的租户
        /// </summary>
        public async Task<IEnumerable<Tenant>> GetUserTenantsAsync(Guid userId)
        {
            _logger.LogInformation("获取用户的租户: {UserId}", userId);
            
            // 获取用户所属的租户
            var user = await _context.Users
                .Include(u => u.Tenant)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null)
            {
                return Enumerable.Empty<Tenant>();
            }

            // 获取用户拥有的租户（作为所有者）
            var ownedTenants = await _context.Tenants
                .Where(t => t.OwnerUserId == userId)
                .ToListAsync();

            // 合并结果并去重
            var result = new List<Tenant>();
            if (user.Tenant != null)
            {
                result.Add(user.Tenant);
            }
            
            foreach (var tenant in ownedTenants)
            {
                if (!result.Any(t => t.Id == tenant.Id))
                {
                    result.Add(tenant);
                }
            }

            return result;
        }

        /// <summary>
        /// 创建租户
        /// </summary>
        public async Task<Tenant> CreateTenantAsync(Tenant tenant)
        {
            _logger.LogInformation("创建租户: {TenantName}, {Domain}", tenant.Name, tenant.Domain);
            
            // 设置创建时间
            tenant.CreatedAt = DateTime.UtcNow;
            
            // 如果未指定订阅到期时间，默认设置为一年后
            if (tenant.SubscriptionExpiresAt == null)
            {
                tenant.SubscriptionExpiresAt = DateTime.UtcNow.AddYears(1);
            }
            
            _context.Tenants.Add(tenant);
            await _context.SaveChangesAsync();
            
            // 创建默认网站配置
            var websiteConfig = new WebsiteConfig
            {
                Id = Guid.NewGuid(),
                TenantId = tenant.Id,
                SiteTitle = $"{tenant.Name} 网站",
                SiteDescription = $"{tenant.Name} 的官方网站",
                SiteKeywords = $"{tenant.Name},MolySite,网站",
                PrimaryColor = "#3b82f6",
                SecondaryColor = "#10b981",
                FooterText = $"© {DateTime.Now.Year} {tenant.Name}. 由 MolySite 提供技术支持。",
                EnableComments = true,
                EnableRegistration = true,
                CreatedAt = DateTime.UtcNow
            };
            
            _context.WebsiteConfigs.Add(websiteConfig);
            await _context.SaveChangesAsync();
            
            return tenant;
        }

        /// <summary>
        /// 更新租户
        /// </summary>
        public async Task<bool> UpdateTenantAsync(Tenant tenant)
        {
            _logger.LogInformation("更新租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
            
            var existingTenant = await _context.Tenants.FindAsync(tenant.Id);
            if (existingTenant == null)
            {
                _logger.LogWarning("租户不存在: {TenantId}", tenant.Id);
                return false;
            }
            
            // 更新租户属性
            existingTenant.Name = tenant.Name;
            existingTenant.Domain = tenant.Domain;
            existingTenant.IsActive = tenant.IsActive;
            existingTenant.Description = tenant.Description;
            existingTenant.SubscriptionPlan = tenant.SubscriptionPlan;
            existingTenant.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt;
            existingTenant.MaxUsers = tenant.MaxUsers;
            existingTenant.MaxStorageMB = tenant.MaxStorageMB;
            existingTenant.Theme = tenant.Theme;
            existingTenant.LogoUrl = tenant.LogoUrl;
            
            _context.Tenants.Update(existingTenant);
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 删除租户
        /// </summary>
        public async Task<bool> DeleteTenantAsync(Guid tenantId)
        {
            _logger.LogInformation("删除租户: {TenantId}", tenantId);
            
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                _logger.LogWarning("租户不存在: {TenantId}", tenantId);
                return false;
            }
            
            // 删除租户的网站配置
            var websiteConfig = await _context.WebsiteConfigs
                .FirstOrDefaultAsync(wc => wc.TenantId == tenantId);
            
            if (websiteConfig != null)
            {
                _context.WebsiteConfigs.Remove(websiteConfig);
            }
            
            // 删除租户
            _context.Tenants.Remove(tenant);
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 获取租户的网站配置
        /// </summary>
        public async Task<WebsiteConfig?> GetWebsiteConfigAsync(Guid tenantId)
        {
            _logger.LogInformation("获取租户的网站配置: {TenantId}", tenantId);
            
            return await _context.WebsiteConfigs
                .FirstOrDefaultAsync(wc => wc.TenantId == tenantId);
        }

        /// <summary>
        /// 更新租户的网站配置
        /// </summary>
        public async Task<bool> UpdateWebsiteConfigAsync(WebsiteConfig config)
        {
            _logger.LogInformation("更新租户的网站配置: {TenantId}", config.TenantId);
            
            var existingConfig = await _context.WebsiteConfigs
                .FirstOrDefaultAsync(wc => wc.TenantId == config.TenantId);
            
            if (existingConfig == null)
            {
                _logger.LogWarning("网站配置不存在: {TenantId}", config.TenantId);
                return false;
            }
            
            // 更新网站配置属性
            existingConfig.SiteTitle = config.SiteTitle;
            existingConfig.SiteDescription = config.SiteDescription;
            existingConfig.SiteKeywords = config.SiteKeywords;
            existingConfig.FaviconUrl = config.FaviconUrl;
            existingConfig.PrimaryColor = config.PrimaryColor;
            existingConfig.SecondaryColor = config.SecondaryColor;
            existingConfig.CustomCss = config.CustomCss;
            existingConfig.CustomJavaScript = config.CustomJavaScript;
            existingConfig.FooterText = config.FooterText;
            existingConfig.SocialMediaLinks = config.SocialMediaLinks;
            existingConfig.NavigationMenu = config.NavigationMenu;
            existingConfig.AnalyticsCode = config.AnalyticsCode;
            existingConfig.EnableComments = config.EnableComments;
            existingConfig.EnableRegistration = config.EnableRegistration;
            existingConfig.UpdatedAt = DateTime.UtcNow;
            
            _context.WebsiteConfigs.Update(existingConfig);
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 获取所有订阅计划
        /// </summary>
        public async Task<IEnumerable<SubscriptionPlan>> GetAllSubscriptionPlansAsync()
        {
            _logger.LogInformation("获取所有订阅计划");
            
            return await _context.SubscriptionPlans
                .Where(sp => sp.IsActive)
                .OrderBy(sp => sp.MonthlyPrice)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定订阅计划
        /// </summary>
        public async Task<SubscriptionPlan?> GetSubscriptionPlanByIdAsync(Guid planId)
        {
            _logger.LogInformation("获取订阅计划: {PlanId}", planId);
            
            return await _context.SubscriptionPlans.FindAsync(planId);
        }

        /// <summary>
        /// 获取指定名称的订阅计划
        /// </summary>
        public async Task<SubscriptionPlan?> GetSubscriptionPlanByNameAsync(string planName)
        {
            _logger.LogInformation("通过名称获取订阅计划: {PlanName}", planName);
            
            return await _context.SubscriptionPlans
                .FirstOrDefaultAsync(sp => sp.Name == planName);
        }

        /// <summary>
        /// 创建订阅计划
        /// </summary>
        public async Task<SubscriptionPlan> CreateSubscriptionPlanAsync(SubscriptionPlan plan)
        {
            _logger.LogInformation("创建订阅计划: {PlanName}", plan.Name);
            
            // 设置创建时间
            plan.CreatedAt = DateTime.UtcNow;
            
            _context.SubscriptionPlans.Add(plan);
            await _context.SaveChangesAsync();
            
            return plan;
        }

        /// <summary>
        /// 更新订阅计划
        /// </summary>
        public async Task<bool> UpdateSubscriptionPlanAsync(SubscriptionPlan plan)
        {
            _logger.LogInformation("更新订阅计划: {PlanId}, {PlanName}", plan.Id, plan.Name);
            
            var existingPlan = await _context.SubscriptionPlans.FindAsync(plan.Id);
            if (existingPlan == null)
            {
                _logger.LogWarning("订阅计划不存在: {PlanId}", plan.Id);
                return false;
            }
            
            // 更新订阅计划属性
            existingPlan.Name = plan.Name;
            existingPlan.Description = plan.Description;
            existingPlan.MonthlyPrice = plan.MonthlyPrice;
            existingPlan.YearlyPrice = plan.YearlyPrice;
            existingPlan.MaxUsers = plan.MaxUsers;
            existingPlan.MaxStorageMB = plan.MaxStorageMB;
            existingPlan.SupportsCustomDomain = plan.SupportsCustomDomain;
            existingPlan.SupportsAdvancedThemes = plan.SupportsAdvancedThemes;
            existingPlan.SupportsApiAccess = plan.SupportsApiAccess;
            existingPlan.SupportsPrioritySupport = plan.SupportsPrioritySupport;
            existingPlan.IsDefault = plan.IsDefault;
            existingPlan.IsActive = plan.IsActive;
            existingPlan.UpdatedAt = DateTime.UtcNow;
            
            _context.SubscriptionPlans.Update(existingPlan);
            
            // 如果这个计划被设置为默认，需要将其他计划设置为非默认
            if (plan.IsDefault)
            {
                var otherDefaultPlans = await _context.SubscriptionPlans
                    .Where(sp => sp.IsDefault && sp.Id != plan.Id)
                    .ToListAsync();
                
                foreach (var otherPlan in otherDefaultPlans)
                {
                    otherPlan.IsDefault = false;
                    _context.SubscriptionPlans.Update(otherPlan);
                }
            }
            
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 删除订阅计划
        /// </summary>
        public async Task<bool> DeleteSubscriptionPlanAsync(Guid planId)
        {
            _logger.LogInformation("删除订阅计划: {PlanId}", planId);
            
            var plan = await _context.SubscriptionPlans.FindAsync(planId);
            if (plan == null)
            {
                _logger.LogWarning("订阅计划不存在: {PlanId}", planId);
                return false;
            }
            
            // 检查是否有租户正在使用此计划
            var tenantsUsingPlan = await _context.Tenants
                .AnyAsync(t => t.SubscriptionPlan == plan.Name);
            
            if (tenantsUsingPlan)
            {
                _logger.LogWarning("无法删除订阅计划，有租户正在使用: {PlanId}, {PlanName}", planId, plan.Name);
                return false;
            }
            
            _context.SubscriptionPlans.Remove(plan);
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 更新租户的订阅计划
        /// </summary>
        public async Task<bool> UpdateTenantSubscriptionAsync(Guid tenantId, string planName, DateTime? expiryDate)
        {
            _logger.LogInformation("更新租户的订阅计划: {TenantId}, {PlanName}, {ExpiryDate}", tenantId, planName, expiryDate);
            
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                _logger.LogWarning("租户不存在: {TenantId}", tenantId);
                return false;
            }
            
            // 检查订阅计划是否存在
            var plan = await _context.SubscriptionPlans
                .FirstOrDefaultAsync(sp => sp.Name == planName && sp.IsActive);
            
            if (plan == null)
            {
                _logger.LogWarning("订阅计划不存在或未激活: {PlanName}", planName);
                return false;
            }
            
            // 更新租户的订阅信息
            tenant.SubscriptionPlan = planName;
            tenant.SubscriptionExpiresAt = expiryDate ?? DateTime.UtcNow.AddYears(1);
            tenant.MaxUsers = plan.MaxUsers;
            tenant.MaxStorageMB = plan.MaxStorageMB;
            
            _context.Tenants.Update(tenant);
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        public async Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeTenantId = null)
        {
            _logger.LogInformation("检查域名是否可用: {Domain}, 排除租户: {ExcludeTenantId}", domain, excludeTenantId);
            
            var query = _context.Tenants.AsQueryable();
            
            if (excludeTenantId.HasValue)
            {
                query = query.Where(t => t.Id != excludeTenantId.Value);
            }
            
            return !await query.AnyAsync(t => t.Domain == domain);
        }
    }
}

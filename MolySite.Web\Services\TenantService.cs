using MolySite.Shared.Models;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;

namespace MolySite.Web.Services
{
    /// <summary>
    /// 租户服务客户端实现
    /// </summary>
    public class TenantService : ITenantService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TenantService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly ApiSettings _apiSettings;

        public TenantService(HttpClient httpClient, IOptions<ApiSettings> apiSettings, ILogger<TenantService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _apiSettings = apiSettings.Value;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        /// <summary>
        /// 获取所有租户（仅限SuperAdmin）
        /// </summary>
        public async Task<List<TenantDto>> GetAllTenantsAsync()
        {
            try
            {
                _logger.LogInformation("获取所有租户");
                var response = await _httpClient.GetFromJsonAsync<List<TenantDto>>($"{_apiSettings.BaseUrl}api/tenant", _jsonOptions);
                return response ?? new List<TenantDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有租户时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取指定租户
        /// </summary>
        public async Task<TenantDto> GetTenantByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("获取租户: {TenantId}", id);
                var response = await _httpClient.GetFromJsonAsync<TenantDto>($"{_apiSettings.BaseUrl}api/tenant/{id}", _jsonOptions);
                return response ?? new TenantDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户时发生错误: {TenantId}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取当前用户的租户
        /// </summary>
        public async Task<List<TenantDto>> GetMyTenantsAsync()
        {
            try
            {
                _logger.LogInformation("获取当前用户的租户");
                var response = await _httpClient.GetFromJsonAsync<List<TenantDto>>($"{_apiSettings.BaseUrl}api/tenant/my", _jsonOptions);
                return response ?? new List<TenantDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前用户的租户时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 创建租户
        /// </summary>
        public async Task<TenantDto> CreateTenantAsync(TenantDto tenant)
        {
            try
            {
                _logger.LogInformation("创建租户: {TenantName}, {Domain}", tenant.Name, tenant.Domain);
                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant", tenant);
                response.EnsureSuccessStatusCode();
                var createdTenant = await response.Content.ReadFromJsonAsync<TenantDto>(_jsonOptions);
                return createdTenant ?? new TenantDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建租户时发生错误: {TenantName}, {Domain}", tenant.Name, tenant.Domain);
                throw;
            }
        }

        /// <summary>
        /// 更新租户
        /// </summary>
        public async Task<bool> UpdateTenantAsync(TenantDto tenant)
        {
            try
            {
                _logger.LogInformation("更新租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                var response = await _httpClient.PutAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant/{tenant.Id}", tenant);
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租户时发生错误: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                throw;
            }
        }

        /// <summary>
        /// 删除租户（仅限SuperAdmin）
        /// </summary>
        public async Task<bool> DeleteTenantAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("删除租户: {TenantId}", id);
                var response = await _httpClient.DeleteAsync($"{_apiSettings.BaseUrl}api/tenant/{id}");
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除租户时发生错误: {TenantId}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取租户的网站配置
        /// </summary>
        public async Task<WebsiteConfigDto> GetWebsiteConfigAsync(Guid tenantId)
        {
            try
            {
                _logger.LogInformation("获取租户的网站配置: {TenantId}", tenantId);
                var response = await _httpClient.GetFromJsonAsync<WebsiteConfigDto>($"{_apiSettings.BaseUrl}api/tenant/{tenantId}/website-config", _jsonOptions);
                return response ?? new WebsiteConfigDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户的网站配置时发生错误: {TenantId}", tenantId);
                throw;
            }
        }

        /// <summary>
        /// 更新租户的网站配置
        /// </summary>
        public async Task<bool> UpdateWebsiteConfigAsync(WebsiteConfigDto config)
        {
            try
            {
                _logger.LogInformation("更新租户的网站配置: {TenantId}", config.TenantId);
                var response = await _httpClient.PutAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant/{config.TenantId}/website-config", config);
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租户的网站配置时发生错误: {TenantId}", config.TenantId);
                throw;
            }
        }

        /// <summary>
        /// 获取所有订阅计划
        /// </summary>
        public async Task<List<SubscriptionPlanDto>> GetAllSubscriptionPlansAsync()
        {
            try
            {
                _logger.LogInformation("获取所有订阅计划");
                var response = await _httpClient.GetFromJsonAsync<List<SubscriptionPlanDto>>($"{_apiSettings.BaseUrl}api/tenant/subscription-plans", _jsonOptions);
                return response ?? new List<SubscriptionPlanDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有订阅计划时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取指定订阅计划
        /// </summary>
        public async Task<SubscriptionPlanDto> GetSubscriptionPlanByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("获取订阅计划: {PlanId}", id);
                var response = await _httpClient.GetFromJsonAsync<SubscriptionPlanDto>($"{_apiSettings.BaseUrl}api/tenant/subscription-plans/{id}", _jsonOptions);
                return response ?? new SubscriptionPlanDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取订阅计划时发生错误: {PlanId}", id);
                throw;
            }
        }

        /// <summary>
        /// 创建订阅计划（仅限SuperAdmin）
        /// </summary>
        public async Task<SubscriptionPlanDto> CreateSubscriptionPlanAsync(SubscriptionPlanDto plan)
        {
            try
            {
                _logger.LogInformation("创建订阅计划: {PlanName}", plan.Name);
                var response = await _httpClient.PostAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant/subscription-plans", plan);
                response.EnsureSuccessStatusCode();
                var createdPlan = await response.Content.ReadFromJsonAsync<SubscriptionPlanDto>(_jsonOptions);
                return createdPlan ?? new SubscriptionPlanDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建订阅计划时发生错误: {PlanName}", plan.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新订阅计划（仅限SuperAdmin）
        /// </summary>
        public async Task<bool> UpdateSubscriptionPlanAsync(SubscriptionPlanDto plan)
        {
            try
            {
                _logger.LogInformation("更新订阅计划: {PlanId}, {PlanName}", plan.Id, plan.Name);
                var response = await _httpClient.PutAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant/subscription-plans/{plan.Id}", plan);
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新订阅计划时发生错误: {PlanId}, {PlanName}", plan.Id, plan.Name);
                throw;
            }
        }

        /// <summary>
        /// 删除订阅计划（仅限SuperAdmin）
        /// </summary>
        public async Task<bool> DeleteSubscriptionPlanAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("删除订阅计划: {PlanId}", id);
                var response = await _httpClient.DeleteAsync($"{_apiSettings.BaseUrl}api/tenant/subscription-plans/{id}");
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除订阅计划时发生错误: {PlanId}", id);
                throw;
            }
        }

        /// <summary>
        /// 更新租户的订阅计划（仅限SuperAdmin）
        /// </summary>
        public async Task<bool> UpdateTenantSubscriptionAsync(Guid tenantId, string planName, DateTime? expiryDate)
        {
            try
            {
                _logger.LogInformation("更新租户的订阅计划: {TenantId}, {PlanName}, {ExpiryDate}", tenantId, planName, expiryDate);
                var request = new
                {
                    PlanName = planName,
                    ExpiryDate = expiryDate
                };
                var response = await _httpClient.PutAsJsonAsync($"{_apiSettings.BaseUrl}api/tenant/{tenantId}/subscription", request);
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租户的订阅计划时发生错误: {TenantId}, {PlanName}", tenantId, planName);
                throw;
            }
        }

        /// <summary>
        /// 检查域名是否可用
        /// </summary>
        public async Task<bool> CheckDomainAvailabilityAsync(string domain, Guid? excludeTenantId = null)
        {
            try
            {
                _logger.LogInformation("检查域名是否可用: {Domain}", domain);
                string url = $"{_apiSettings.BaseUrl}api/tenant/check-domain?domain={Uri.EscapeDataString(domain)}";
                if (excludeTenantId.HasValue)
                {
                    url += $"&excludeTenantId={excludeTenantId.Value}";
                }
                var response = await _httpClient.GetFromJsonAsync<bool>(url, _jsonOptions);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查域名是否可用时发生错误: {Domain}", domain);
                throw;
            }
        }
    }
}

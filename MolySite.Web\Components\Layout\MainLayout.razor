﻿@inherits LayoutComponentBase
@using MolySite.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@inject NavigationManager NavigationManager
@inject IAuthService AuthService

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <AuthorizeView>
                <Authorized>
                    <div class="ms-auto d-flex align-items-center">
                        <span class="me-3">欢迎, @context.User.Identity?.Name!</span>
                        <button @onclick="LogoutAsync" class="btn btn-outline-danger btn-sm">退出</button>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="ms-auto">
                        <a href="/login" class="btn btn-outline-primary btn-sm">登录</a>
                        <a href="/register" class="btn btn-outline-secondary btn-sm ms-2">注册</a>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

<style>
    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
</style>

@code {
    private async Task LogoutAsync()
    {
        await AuthService.LogoutAsync();
        NavigationManager.NavigateTo("/", forceLoad: true);
    }
}

using System;
using System.ComponentModel.DataAnnotations;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 令牌存储模型
    /// </summary>
    public class TokenStore
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 访问令牌过期时间
        /// </summary>
        public DateTime AccessTokenExpiration { get; set; }

        /// <summary>
        /// 刷新令牌过期时间
        /// </summary>
        public DateTime RefreshTokenExpiration { get; set; }

        /// <summary>
        /// 令牌是否已被吊销
        /// </summary>
        public bool IsRevoked { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }
    }

    /// <summary>
    /// 令牌黑名单模型
    /// </summary>
    public class TokenBlacklist
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// 被吊销的令牌
        /// </summary>
        public string? Token { get; set; }

        /// <summary>
        /// 吊销时间
        /// </summary>
        public DateTime RevokedAt { get; set; }

        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime Expiration { get; set; }
    }
} 
@page "/dashboard/superadmin/tenants/create"
@using Microsoft.AspNetCore.Authorization
@using MolySite.Shared.Models
@using MolySite.Web.Services
@using System.Threading
@inject ITenantService TenantService
@inject NavigationManager NavigationManager
@inject ILogger<Create> Logger
@attribute [Authorize(Roles = "SuperAdmin")]

<PageTitle>创建租户 - MolySite</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <button @onclick="@(() => NavigationManager.NavigateTo("/dashboard/superadmin/tenants"))" 
                class="mr-4 text-gray-600 hover:text-gray-900">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
        </button>
        <h1 class="text-2xl font-bold text-gray-800">创建租户</h1>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
        <EditForm Model="@_tenant" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">租户名称</label>
                    <InputText id="name" @bind-Value="_tenant.Name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    <ValidationMessage For="@(() => _tenant.Name)" class="text-red-500 text-xs mt-1" />
                </div>

                <div class="mb-4">
                    <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">租户域名</label>
                    <div class="flex">
                        <InputText id="domain" @bind-Value="_tenant.Domain" 
                                  @oninput="@(async (e) => await CheckDomainAvailability(e.Value?.ToString()))"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>
                    <ValidationMessage For="@(() => _tenant.Domain)" class="text-red-500 text-xs mt-1" />
                    @if (_checkingDomain)
                    {
                        <span class="text-gray-500 text-xs mt-1">检查域名可用性...</span>
                    }
                    else if (_domainChecked)
                    {
                        @if (_isDomainAvailable)
                        {
                            <span class="text-green-500 text-xs mt-1">域名可用</span>
                        }
                        else
                        {
                            <span class="text-red-500 text-xs mt-1">域名已被使用</span>
                        }
                    }
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">租户描述</label>
                    <InputTextArea id="description" @bind-Value="_tenant.Description" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" rows="3" />
                </div>

                <div class="mb-4">
                    <label for="subscriptionPlan" class="block text-sm font-medium text-gray-700 mb-1">订阅计划</label>
                    <InputSelect id="subscriptionPlan" @bind-Value="_tenant.SubscriptionPlan" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        @foreach (var plan in _subscriptionPlans)
                        {
                            <option value="@plan.Name">@plan.Name</option>
                        }
                    </InputSelect>
                </div>

                <div class="mb-4">
                    <label for="expiryDate" class="block text-sm font-medium text-gray-700 mb-1">订阅到期时间</label>
                    <InputDate id="expiryDate" @bind-Value="_tenant.SubscriptionExpiresAt" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
                </div>

                <div class="mb-4">
                    <label class="flex items-center">
                        <InputCheckbox @bind-Value="_tenant.IsActive" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                        <span class="ml-2 text-sm text-gray-700">租户状态（活跃）</span>
                    </label>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="button" @onclick="@(() => NavigationManager.NavigateTo("/dashboard/superadmin/tenants"))" 
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded mr-2">
                    取消
                </button>
                <button type="submit" disabled="@(_submitting || !_isDomainAvailable)" 
                        class="@(_submitting || !_isDomainAvailable ? "bg-blue-300" : "bg-blue-500 hover:bg-blue-600") text-white font-medium py-2 px-4 rounded">
                    @if (_submitting)
                    {
                        <span>提交中...</span>
                    }
                    else
                    {
                        <span>创建租户</span>
                    }
                </button>
            </div>
        </EditForm>
    </div>
</div>

@code {
    private MolySite.Shared.Models.TenantDto _tenant = new()
    {
        IsActive = true,
        SubscriptionExpiresAt = DateTime.UtcNow.AddYears(1)
    };
    private List<SubscriptionPlanDto> _subscriptionPlans = new();
    private bool _submitting = false;
    private bool _checkingDomain = false;
    private bool _domainChecked = false;
    private bool _isDomainAvailable = false;
    private Timer? _domainCheckTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadSubscriptionPlans();
    }

    private async Task LoadSubscriptionPlans()
    {
        try
        {
            _subscriptionPlans = await TenantService.GetAllSubscriptionPlansAsync();
            
            // 设置默认订阅计划
            var defaultPlan = _subscriptionPlans.FirstOrDefault(p => p.IsDefault);
            if (defaultPlan != null)
            {
                _tenant.SubscriptionPlan = defaultPlan.Name;
            }
            else if (_subscriptionPlans.Any())
            {
                _tenant.SubscriptionPlan = _subscriptionPlans.First().Name;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载订阅计划时发生错误");
        }
    }

    private async Task CheckDomainAvailability(string? domain)
    {
        if (string.IsNullOrWhiteSpace(domain))
        {
            _domainChecked = false;
            _isDomainAvailable = false;
            return;
        }

        _checkingDomain = true;
        _domainChecked = false;
        
        // 使用计时器延迟检查，避免频繁API调用
        _domainCheckTimer?.Dispose();
        _domainCheckTimer = new Timer(async state =>
        {
            try
            {
                _isDomainAvailable = await TenantService.CheckDomainAvailabilityAsync(domain);
                _domainChecked = true;
                _checkingDomain = false;
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "检查域名可用性时发生错误");
                _checkingDomain = false;
                await InvokeAsync(StateHasChanged);
            }
        }, null, 500, Timeout.Infinite);
    }

    private async Task HandleValidSubmit()
    {
        if (string.IsNullOrWhiteSpace(_tenant.Name) || string.IsNullOrWhiteSpace(_tenant.Domain))
        {
            return;
        }

        try
        {
            _submitting = true;
            
            // 再次检查域名可用性
            _isDomainAvailable = await TenantService.CheckDomainAvailabilityAsync(_tenant.Domain);
            if (!_isDomainAvailable)
            {
                return;
            }

            // 创建租户
            var createdTenant = await TenantService.CreateTenantAsync(_tenant);
            
            // 跳转到租户列表页
            NavigationManager.NavigateTo("/dashboard/superadmin/tenants");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "创建租户时发生错误");
        }
        finally
        {
            _submitting = false;
        }
    }
} 
using System;
using System.Threading.Tasks;

namespace MolySite.Identity.Services
{
    /// <summary>
    /// 密码服务接口
    /// </summary>
    public interface IPasswordService
    {
        /// <summary>
        /// 生成密码重置令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>密码重置令牌</returns>
        Task<string> GeneratePasswordResetTokenAsync(Guid userId);

        /// <summary>
        /// 验证密码重置令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="token">重置令牌</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidatePasswordResetTokenAsync(Guid userId, string token);

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="token">重置令牌</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>是否重置成功</returns>
        Task<bool> ResetPasswordAsync(Guid userId, string token, string newPassword);

        /// <summary>
        /// 发送密码重置邮件
        /// </summary>
        /// <param name="email">用户邮箱</param>
        /// <returns>是否发送成功</returns>
        Task<bool> SendPasswordResetEmailAsync(string email);
    }
} 
using System;
using System.Text.Json;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 网站配置模型
    /// </summary>
    public class WebsiteConfig
    {
        /// <summary>
        /// 网站配置ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 关联的租户ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// 导航属性：关联的租户
        /// </summary>
        public virtual Tenant? Tenant { get; set; }

        /// <summary>
        /// 网站标题
        /// </summary>
        public string SiteTitle { get; set; } = "新网站";

        /// <summary>
        /// 网站描述
        /// </summary>
        public string? SiteDescription { get; set; }

        /// <summary>
        /// 网站关键词
        /// </summary>
        public string? SiteKeywords { get; set; }

        /// <summary>
        /// 网站图标URL
        /// </summary>
        public string? FaviconUrl { get; set; }

        /// <summary>
        /// 主色调
        /// </summary>
        public string PrimaryColor { get; set; } = "#3b82f6";

        /// <summary>
        /// 次要色调
        /// </summary>
        public string SecondaryColor { get; set; } = "#10b981";

        /// <summary>
        /// 自定义CSS
        /// </summary>
        public string? CustomCss { get; set; }

        /// <summary>
        /// 自定义JavaScript
        /// </summary>
        public string? CustomJavaScript { get; set; }

        /// <summary>
        /// 页脚文本
        /// </summary>
        public string? FooterText { get; set; }

        /// <summary>
        /// 社交媒体链接（JSON格式）
        /// </summary>
        public string? SocialMediaLinks { get; set; }

        /// <summary>
        /// 导航菜单（JSON格式）
        /// </summary>
        public string? NavigationMenu { get; set; }

        /// <summary>
        /// 分析代码（如Google Analytics）
        /// </summary>
        public string? AnalyticsCode { get; set; }

        /// <summary>
        /// 是否启用评论功能
        /// </summary>
        public bool EnableComments { get; set; } = true;

        /// <summary>
        /// 是否启用注册功能
        /// </summary>
        public bool EnableRegistration { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 将社交媒体链接反序列化为字典
        /// </summary>
        public Dictionary<string, string>? GetSocialMediaLinks()
        {
            if (string.IsNullOrEmpty(SocialMediaLinks))
                return null;

            return JsonSerializer.Deserialize<Dictionary<string, string>>(SocialMediaLinks);
        }

        /// <summary>
        /// 设置社交媒体链接
        /// </summary>
        public void SetSocialMediaLinks(Dictionary<string, string> links)
        {
            SocialMediaLinks = JsonSerializer.Serialize(links);
        }
    }
} 
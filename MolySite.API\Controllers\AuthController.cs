using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MolySite.Identity.Dtos;
using MolySite.Identity.Services;

namespace MolySite.API.Controllers
{
    /// <summary>
    /// 身份验证控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService, 
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerDto">注册信息</param>
        /// <returns>注册结果</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
        {
            try
            {
                _logger.LogInformation("收到注册请求: {UserName}, {Email}", registerDto.UserName, registerDto.Email);
                
                var result = await _authService.RegisterAsync(registerDto);
                
                if (result.Success)
                {
                    _logger.LogInformation("注册成功: {UserId}", result.UserId);
                    return Ok(new { 
                        success = true, 
                        userId = result.UserId,
                        message = "注册成功"
                    });
                }
                else
                {
                    _logger.LogWarning("注册失败: {ErrorMessage}", result.ErrorMessage);
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = result.ErrorMessage 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生错误");
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = "服务器内部错误" 
                });
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录令牌</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            try
            {
                _logger.LogInformation("收到登录请求: {UserName}", loginDto.UserNameOrEmail);
                
                var result = await _authService.LoginAsync(loginDto);
                
                if (result.Success)
                {
                    _logger.LogInformation("登录成功: {UserId}", result.LoginResponse?.UserId);
                    return Ok(new { 
                        success = true, 
                        loginResponse = result.LoginResponse 
                    });
                }
                else
                {
                    _logger.LogWarning("登录失败: {ErrorMessage}", result.ErrorMessage);
                    return Unauthorized(new { 
                        success = false, 
                        errorMessage = result.ErrorMessage 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生错误");
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = "服务器内部错误" 
                });
            }
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>新的访问令牌</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] string refreshToken)
        {
            try
            {
                _logger.LogInformation("收到刷新令牌请求");
                
                var result = await _authService.RefreshTokenAsync(refreshToken);
                
                if (result.Success)
                {
                    _logger.LogInformation("刷新令牌成功: {UserId}", result.LoginResponse?.UserId);
                    return Ok(new { 
                        success = true, 
                        loginResponse = result.LoginResponse 
                    });
                }
                else
                {
                    _logger.LogWarning("刷新令牌失败: {ErrorMessage}", result.ErrorMessage);
                    return Unauthorized(new { 
                        success = false, 
                        errorMessage = result.ErrorMessage 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌过程中发生错误");
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = "服务器内部错误" 
                });
            }
        }

        /// <summary>
        /// 获取默认租户
        /// </summary>
        /// <returns>默认租户信息</returns>
        [HttpGet("default-tenant")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDefaultTenant()
        {
            try
            {
                _logger.LogInformation("收到获取默认租户请求");
                
                // 从 Identity 服务获取默认租户
                var tenant = await _authService.GetDefaultTenantAsync();
                
                if (tenant != null)
                {
                    _logger.LogInformation("返回默认租户: {TenantId}, {TenantName}", tenant.Id, tenant.Name);
                    return Ok(new { 
                        success = true, 
                        tenant = new { 
                            id = tenant.Id, 
                            name = tenant.Name 
                        } 
                    });
                }
                else
                {
                    _logger.LogWarning("未找到默认租户");
                    return NotFound(new { 
                        success = false, 
                        errorMessage = "未找到默认租户" 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认租户时发生错误");
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = "服务器内部错误" 
                });
            }
        }
    }
} 
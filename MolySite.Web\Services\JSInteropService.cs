using Microsoft.JSInterop;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MolySite.Web.Services
{
    /// <summary>
    /// JavaScript 互操作服务实现
    /// </summary>
    public class JSInteropService : IJSInteropService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ILogger<JSInteropService> _logger;

        public JSInteropService(IJSRuntime jsRuntime, ILogger<JSInteropService> logger)
        {
            _jsRuntime = jsRuntime;
            _logger = logger;
        }

        /// <summary>
        /// 刷新页面
        /// </summary>
        public async Task RefreshPage()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("window.location.reload");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新页面失败");
            }
        }
        
        /// <summary>
        /// 强制刷新页面（清除缓存）
        /// </summary>
        public async Task ForceRefreshPage()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("window.location.reload", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "强制刷新页面失败");
            }
        }
        
        /// <summary>
        /// 重定向到指定URL
        /// </summary>
        public async Task RedirectTo(string url)
        {
            try
            {
                _logger.LogInformation("执行页面重定向: {Url}", url);
                // 使用replace避免在历史记录中添加条目，防止返回按钮问题
                await _jsRuntime.InvokeVoidAsync("window.location.replace", url);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重定向失败: {Url}, {Error}", url, ex.Message);
            }
        }
        
        /// <summary>
        /// 获取当前URL
        /// </summary>
        public async Task<string> GetCurrentUrl()
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("blazorHelpers.getCurrentUrl");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前URL失败");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 显示浏览器警告
        /// </summary>
        public async Task ShowAlert(string message)
        {
            await _jsRuntime.InvokeVoidAsync("alert", message);
        }
        
        /// <summary>
        /// 设置本地存储
        /// </summary>
        public async Task SetLocalStorage(string key, string value)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", key, value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置本地存储失败: {Key}", key);
            }
        }
        
        /// <summary>
        /// 获取本地存储
        /// </summary>
        public async Task<string> GetLocalStorage(string key)
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("localStorage.getItem", key) ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取本地存储失败: {Key}", key);
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 删除本地存储
        /// </summary>
        public async Task RemoveLocalStorage(string key)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除本地存储失败: {Key}", key);
            }
        }
        
        /// <summary>
        /// 获取所有Cookie
        /// </summary>
        public async Task<string> GetAllCookies()
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("eval", "document.cookie");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有Cookie失败");
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 设置Cookie
        /// </summary>
        public async Task SetCookie(string name, string value, int days = 7)
        {
            try
            {
                var expires = days > 0 ? $"expires={DateTime.Now.AddDays(days).ToUniversalTime():R}" : string.Empty;
                await _jsRuntime.InvokeVoidAsync("eval", $"document.cookie = '{name}={value}; path=/; {expires}'");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置Cookie失败: {Name}", name);
            }
        }
        
        /// <summary>
        /// 获取Cookie
        /// </summary>
        public async Task<string> GetCookie(string name)
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("eval", $"document.cookie.split(';').find(c => c.trim().startsWith('{name}='))?.split('=')[1] || ''");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Cookie失败: {Name}", name);
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 删除Cookie
        /// </summary>
        public async Task DeleteCookie(string name)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("eval", $"document.cookie = '{name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除Cookie失败: {Name}", name);
            }
        }

        public Task EraseCookie(string name)
        {
            return DeleteCookie(name);
        }

        public async Task<string> GetSessionStorage(string key)
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("sessionStorage.getItem", key) ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话存储失败: {Key}", key);
                return string.Empty;
            }
        }

        public async Task SetSessionStorage(string key, string value)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("sessionStorage.setItem", key, value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置会话存储失败: {Key}", key);
            }
        }

        public async Task RemoveSessionStorage(string key)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("sessionStorage.removeItem", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除会话存储失败: {Key}", key);
            }
        }

        /// <summary>
        /// 检查是否可以调用JS方法
        /// </summary>
        public async Task<bool> CanInvokeJSMethod()
        {
            try
            {
                // 尝试调用一个简单的JS方法来检查JS是否可用
                await _jsRuntime.InvokeAsync<bool>("eval", "true");
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 执行任意JavaScript代码（无返回值）
        /// </summary>
        public async Task InvokeVoidAsync(string identifier, params object[] args)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync(identifier, args);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行JavaScript代码失败: {Identifier}", identifier);
            }
        }
        
        /// <summary>
        /// 执行任意JavaScript代码（有返回值）
        /// </summary>
        public async Task<TValue> InvokeAsync<TValue>(string identifier, params object[] args)
        {
            try
            {
                return await _jsRuntime.InvokeAsync<TValue>(identifier, args);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行JavaScript代码失败: {Identifier}", identifier);
                return default;
            }
        }
    }
} 
using System;
using System.Collections.Generic;

namespace MolySite.Identity.Models
{
    /// <summary>
    /// 租户模型，表示多租户系统中的租户
    /// </summary>
    public class Tenant
    {
        /// <summary>
        /// 租户唯一标识符
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 租户名称
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// 租户域名
        /// </summary>
        public required string Domain { get; set; }

        /// <summary>
        /// 租户状态（活跃/禁用）
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 租户创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 租户描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 租户所有者ID
        /// </summary>
        public Guid OwnerUserId { get; set; }

        /// <summary>
        /// 租户订阅计划（例如：Free, Basic, Premium, Enterprise）
        /// </summary>
        public string SubscriptionPlan { get; set; } = "Free";

        /// <summary>
        /// 租户订阅到期时间
        /// </summary>
        public DateTime? SubscriptionExpiresAt { get; set; }

        /// <summary>
        /// 租户最大用户数
        /// </summary>
        public int MaxUsers { get; set; } = 5;

        /// <summary>
        /// 租户最大存储空间（MB）
        /// </summary>
        public int MaxStorageMB { get; set; } = 100;

        /// <summary>
        /// 租户网站配置（JSON格式）
        /// </summary>
        public string? WebsiteConfig { get; set; }

        /// <summary>
        /// 租户主题
        /// </summary>
        public string? Theme { get; set; } = "Default";

        /// <summary>
        /// 租户Logo URL
        /// </summary>
        public string? LogoUrl { get; set; }

        /// <summary>
        /// 导航属性：租户用户
        /// </summary>
        public virtual ICollection<ApplicationUser>? Users { get; set; }
    }
} 